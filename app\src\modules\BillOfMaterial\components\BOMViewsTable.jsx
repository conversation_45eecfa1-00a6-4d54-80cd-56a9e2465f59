import React from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Typography,
  Button,
  Box,
  IconButton,
  Paper,
  Chip,
  Stack,
  Tooltip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import TextField from "@mui/material/TextField";
import Checkbox from "@mui/material/Checkbox";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { styled } from "@mui/material/styles";
import { colors } from "@constant/colors";
import TableRowsIcon from "@mui/icons-material/TableRows";
import { useSelector } from "react-redux";
import { ERROR_MESSAGES } from "@constant/enum";

const StyledDataGridContainer = styled(Paper)(({ theme }) => ({
  boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
  borderRadius: "16px",
  overflow: "hidden",
  border: `1px solid ${colors.primary.border}`,
  "& .MuiDataGrid-root": {
    border: "none",
    "& .MuiDataGrid-cell": {
      borderBottom: `1px solid ${colors.primary.pale}`,
      padding: "8px 16px",
      "&:focus": {
        outline: "none",
      },
      "&:focus-within": {
        outline: `2px solid ${colors.primary.main}`,
        outlineOffset: "-2px",
      },
    },
    "& .MuiDataGrid-columnHeaders": {
      backgroundColor: colors.primary.ultraLight,
      borderBottom: `2px solid ${colors.primary.border}`,
      "& .MuiDataGrid-columnHeader": {
        padding: "12px 16px",
        "&:focus": {
          outline: "none",
        },
        "&:focus-within": {
          outline: `2px solid ${colors.primary.main}`,
          outlineOffset: "-2px",
        },
      },
    },
    "& .MuiDataGrid-row": {
      "&:nth-of-type(even)": {
        backgroundColor: colors.primary.veryLight,
      },
      "&:hover": {
        backgroundColor: `${colors.primary.light}40`,
      },
      "&.Mui-selected": {
        backgroundColor: `${colors.primary.main}20`,
        "&:hover": {
          backgroundColor: `${colors.primary.main}30`,
        },
      },
    },
    "& .MuiDataGrid-virtualScroller": {
      "&::-webkit-scrollbar": {
        width: "8px",
        height: "8px",
      },
      "&::-webkit-scrollbar-track": {
        backgroundColor: colors.primary.veryLight,
      },
      "&::-webkit-scrollbar-thumb": {
        backgroundColor: colors.primary.pale,
        borderRadius: "4px",
        "&:hover": {
          backgroundColor: colors.primary.border,
        },
      },
    },
  },
}));

const StyledToolbar = styled(Box)(({ theme }) => ({
  padding: "16px 20px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  borderBottom: `1px solid ${colors.primary.border}`,
  backgroundColor: colors.primary.ultraLight,
  gap: "16px",
}));

const StyledNoRowsOverlay = styled("div")(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  height: "100%",
  padding: "32px",
  backgroundColor: colors.primary.veryLight,
  "& svg": {
    color: colors.secondary.grey,
    fontSize: "48px",
    marginBottom: "16px",
  },
}));

function CustomNoRowsOverlay() {
  return (
    <StyledNoRowsOverlay>
      <TableRowsIcon />
      <Typography
        variant="h6"
        sx={{
          color: colors.secondary.grey,
          fontWeight: 500,
          marginBottom: "8px",
        }}
      >
        {ERROR_MESSAGES.NO_DATA_AVAILABLE}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: colors.secondary.grey,
          textAlign: "center",
        }}
      >
        {ERROR_MESSAGES.NO_RECORDS}
      </Typography>
    </StyledNoRowsOverlay>
  );
}

const BOMViewsTable = ({ viewsDt, activeViewName, rows, onRowsUpdate }) => {
  const fields = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.filter((field) => field.MDG_MAT_VIEW_NAME === activeViewName)
    ?.sort(
      (a, b) => (a.MDG_MAT_SEQUENCE_NO ?? 0) - (b.MDG_MAT_SEQUENCE_NO ?? 0)
    );
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const cardName = viewsDt
    ?.flatMap((item) => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
    ?.find(
      (field) => field.MDG_MAT_VIEW_NAME === activeViewName
    )?.MDG_MAT_CARD_NAME;

  if (!fields || fields.length === 0) {
    return (
      <StyledDataGridContainer>
        <StyledNoRowsOverlay>
          <TableRowsIcon />
          <Typography
            variant="h6"
            sx={{
              color: colors.secondary.grey,
              fontWeight: 500,
              marginBottom: "8px",
            }}
          >
            {ERROR_MESSAGES.NO_FIELDS_CONFIGURED}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: colors.secondary.grey,
              textAlign: "center",
            }}
          >
            {ERROR_MESSAGES.NO_DATA_CONFIGURED}
          </Typography>
        </StyledNoRowsOverlay>
      </StyledDataGridContainer>
    );
  }

  const handleAddRow = () => {
    const newRow = {
      id: Date.now(),
      ...fields.reduce((acc, field) => {
        acc[field.MDG_MAT_JSON_FIELD_NAME || field.MDG_MAT_UI_FIELD_NAME] = "";
        return acc;
      }, {}),
    };
    onRowsUpdate([...rows, newRow]);
  };

  const handleDeleteRow = (rowId) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    onRowsUpdate(updatedRows);
  };

  const renderCell = (field, value, rowId) => {
    const fieldName =
      field.MDG_MAT_JSON_FIELD_NAME || field.MDG_MAT_UI_FIELD_NAME;

    switch (field.MDG_MAT_FIELD_TYPE) {
      case "Input":
        return (
          <TextField
            size="small"
            value={value || ""}
            onChange={(e) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId ? { ...row, [fieldName]: e.target.value } : row
              );
              onRowsUpdate(updatedRows);
            }}
            fullWidth
            variant="outlined"
            placeholder={`ENTER ${field.MDG_MAT_UI_FIELD_NAME.toUpperCase()}`}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: colors.primary.pale,
                },
                "&:hover fieldset": {
                  borderColor: colors.primary.border,
                },
                "&.Mui-focused fieldset": {
                  borderColor: colors.primary.main,
                },
              },
            }}
          />
        );
      case "Drop Down":
        return (
          <SingleSelectDropdown
            options={allDropDownData[fieldName] || []}
            value={value || ""}
            onChange={(newValue) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId ? { ...row, [fieldName]: newValue } : row
              );
              onRowsUpdate(updatedRows);
            }}
            placeholder={`SELECT ${field.MDG_MAT_UI_FIELD_NAME.toUpperCase()}`}
          />
        );
      case "Check Box":
        return (
          <Checkbox
            checked={value || false}
            onChange={(e) => {
              const updatedRows = rows.map((row) =>
                row.id === rowId
                  ? { ...row, [fieldName]: e.target.checked }
                  : row
              );
              onRowsUpdate(updatedRows);
            }}
            sx={{
              color: colors.primary.main,
              "&.Mui-checked": {
                color: colors.primary.main,
              },
            }}
          />
        );
      default:
        return (
          <Typography
            variant="body2"
            sx={{
              color: value ? colors.text.primary : colors.text.disabled,
            }}
          >
            {value || "-"}
          </Typography>
        );
    }
  };

  const columns = [
    ...fields.map((field) => ({
      field: field.MDG_MAT_JSON_FIELD_NAME || field.MDG_MAT_UI_FIELD_NAME,
      headerName: field.MDG_MAT_UI_FIELD_NAME,
      flex: 1,
      minWidth: 120,
      renderCell: (params) => renderCell(field, params.value, params.row.id),
    })),

    ...(activeViewName !== "General"
      ? [
          {
            field: "actions",
            headerName: "Actions",
            width: 100,
            sortable: false,
            filterable: false,
            renderCell: (params) => (
              <Tooltip title="Delete Row">
                <IconButton
                  onClick={() => handleDeleteRow(params.row.id)}
                  color="error"
                  size="small"
                  sx={{
                    "&:hover": {
                      backgroundColor: `${colors.error.light}40`,
                    },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            ),
          },
        ]
      : []),
  ];

  return (
    <StyledDataGridContainer>
      <StyledToolbar>
        <Stack direction="row" spacing={2} alignItems="center">
          {cardName && (
            <Typography
              variant="h6"
              sx={{
                color: colors.primary.main,
              }}
            >
              {cardName}
            </Typography>
          )}
          <Chip
            label={`${rows.length} records`}
            size="small"
            sx={{
              backgroundColor: colors.primary.light,
              color: colors.primary.main,
              height: "24px",
              fontSize: "0.75rem",
            }}
          />
        </Stack>

        {activeViewName !== "General" && (
          <Tooltip title="Add New Row">
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddRow}
              size="small"
              sx={{
                backgroundColor: colors.primary.main,
                "&:hover": {
                  backgroundColor: colors.primary.dark,
                },
                borderRadius: "8px",
                textTransform: "none",
                fontWeight: 500,
              }}
            >
              Add Row
            </Button>
          </Tooltip>
        )}
      </StyledToolbar>
      <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
        <div style={{ height: "100%" }}>
          <DataGrid
            columns={columns}
            rows={rows}
            autoHeight={false}
            disableSelectionOnClick
            hideFooter
            components={{
              NoRowsOverlay: CustomNoRowsOverlay,
            }}
            style={{
              border: "1px solid #ccc",
              borderRadius: "8px",
              width: "100%",
              height:
                rows.length === 0
                  ? "180px"
                  : `${Math.min(rows.length * 50 + 100, 300)}px`,
              overflow: "auto",
            }}
          />
        </div>
      </div>
    </StyledDataGridContainer>
  );
};

export default BOMViewsTable;
