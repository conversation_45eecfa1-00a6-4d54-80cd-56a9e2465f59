import { combineReducers } from "@reduxjs/toolkit";

import initialDataReducer from "./initialDataSlice";
import tabsDetailsReducer from "./tabsDetailsSlice";
import dropDownDataReducer from "./dropDownDataSlice";
import commonFilterReducer from "./commonFilterSlice";
import selectedSectionsReducer from "./selectedSelectionsSlice";
import userManagementReducer from "./userManagementSlice";
import masterDataReducer from "./masterDataSlice";
import applicationConfigReducer from "./applicationConfigReducer";
import commonSearchBarReducer from "./commonSearchBarSlice";
import appSettingsReducer from "./appSettingsSlice";
import userReducer from "./userReducer";
import { payloadReducer } from "./payloadslice";
import { costCenterReducer } from "./costCenterTabsSlice";
import { profitCenterReducer } from "./profitCenterTabsSlice";
import { editPayloadReducer } from "./editPayloadSlice";
import { bankKeyReducer } from "./bankKeyTabSlice";
import { generalLedgerReducer } from "./generalLedgerTabSlice";
import notificationSlice from "./notificationSlice";
import { requestSliceReducer } from "./requestDataSlice";
import { changeLogReducer } from "./changeLogReducer";
import { paginationReducer } from "./paginationSlice";
import { snackBarReducer } from "./snackbarSlice";
import { LOGOUT } from "../constant/enum";
import stepperReducer from "./redux/stepperSlice";
import profitCenterTabsReducer from "./redux/profitCenterTabsSlice";
import profitCenterTabReducer from "./redux/profitCenterTabSlice";
import requestHeaderReducer from "./redux/requestHeaderSlice";
import { hierarchyReducer } from "./hierarchyDataSlice";
import materialDropDownDataSlice from "@material/slice/materialDropdownSlice";
import profitCenterDropdownSlice from "@profitCenter/slice/profitCenterDropdownSlice";
import costCenterDropDownSlice from "@costCenter/slice/costCenterDropDownSlice";
import generalLedgerDropDownSlice from "@generalLedger/slice/generalLedgerDropDownSlice";
import { default as bomReducer } from "@BillOfMaterial/bomSlice";
import internalOrderReducer from "@InternalOrder/slice/internalOrderSlice";
import {documentApi} from "@api/document/DocumentApiService"

const appReducer = combineReducers({
  initialData: initialDataReducer,
  commonFilter: commonFilterReducer,
  commonSearchBar: commonSearchBarReducer,
  selectedSections: selectedSectionsReducer,
  userManagement: userManagementReducer,
  userReducer: userReducer,
  masterData: masterDataReducer,
  applicationConfig: applicationConfigReducer,
  appSettings: appSettingsReducer,
  tabsData: tabsDetailsReducer,
  payload: payloadReducer,
  paginationData: paginationReducer,
  changeLog: changeLogReducer,
  AllDropDown: dropDownDataReducer,
  costCenter: costCenterReducer,
  profitCenter: profitCenterReducer,
  bankKey: bankKeyReducer,
  generalLedger: generalLedgerReducer,
  edit: editPayloadReducer,
  notifications: notificationSlice,
  request: requestSliceReducer,
  snackbar: snackBarReducer,
  CommonStepper: stepperReducer,
  requestHeader: requestHeaderReducer,
  profitCenterTabs: profitCenterTabsReducer,
  profitCenterTab: profitCenterTabReducer,
  hierarchyData: hierarchyReducer,
  materialDropDownData: materialDropDownDataSlice,
  profitCenterDropdownData: profitCenterDropdownSlice,
  costCenterDropDownData: costCenterDropDownSlice,
  generalLedgerDropDownData: generalLedgerDropDownSlice,
  bom: bomReducer,
  internalOrder: internalOrderReducer,
  [documentApi.reducerPath]:documentApi.reducer
});

const rootReducer = (state, action) => {
  if (action.type === LOGOUT) {
    state = undefined;
  }

  return appReducer(state, action);
};

export default rootReducer;
