import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getLocalStorage, groupBy } from "@helper/helper";
import { destination_IDM } from "../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, LOCAL_STORAGE_KEYS } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";

const transformInternalOrderFieldConfigData = (responseData) => {
  let mandatoryFields = {};
  let sortedData = responseData?.sort((a, b) => a.MDG_IO_SEQUENCE_NO - b.MDG_IO_SEQUENCE_NO);
  const groupedFields = groupBy(sortedData, "MDG_IO_VIEW_NAME");

  let view_data_array = [];
  Object.entries(groupedFields).forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_IO_CARD_NAME");
    let cards = [];
    
    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      cardFields.sort((a, b) => a.MDG_IO_SEQUENCE_NO - b.MDG_IO_SEQUENCE_NO);

      let cardDetails = cardFields.map((item) => {
        if (item.MDG_IO_VISIBILITY === "Mandatory") {
          if (!mandatoryFields[viewName]) {
            mandatoryFields[viewName] = [];
          }
          mandatoryFields[viewName].push(item.MDG_IO_JSON_FIELD_NAME);
        }

        return {
          fieldName: item.MDG_IO_UI_FIELD_NAME,
          sequenceNo: item.MDG_IO_SEQUENCE_NO,
          fieldType: item.MDG_IO_FIELD_TYPE,
          maxLength: item.MDG_IO_MAX_LENGTH,
          dataType: item.MDG_IO_DATA_TYPE,
          viewName: item.MDG_IO_VIEW_NAME,
          cardName: item.MDG_IO_CARD_NAME,
          cardSeq: item.MDG_IO_CARD_SEQUENCE,
          value: item.MDG_IO_DEFAULT_VALUE,
          visibility: item.MDG_IO_VISIBILITY,
          jsonName: item.MDG_IO_JSON_FIELD_NAME,
        };
      });

      cards.push({
        cardName,
        cardSeq: cardFields[0].MDG_IO_CARD_SEQUENCE,
        cardDetails,
      });
    });

    cards.sort((a, b) => a.cardSeq - b.cardSeq);
    view_data_array.push({ viewName, cards });
  });

  const transformedData = {};
  view_data_array.forEach((view) => {
    transformedData[view.viewName] = view;
  });

  return { transformedData, mandatoryFields };
};

const useInternalOrderFieldConfig = () => {
  const dispatch = useDispatch();
  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state.payload?.payloadData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const { userData, taskData } = useSelector((state) => state.userManagement);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
  let finalTaskData = null;
  finalTaskData = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
  let workflowGroup = finalTaskData?.ATTRIBUTE_5;

  const getInternalOrderFieldConfig = async (orderType) => {
    setLoading(true);
    
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_INTORD_FIELD_CONFIG",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": initialPayload?.RequestType || "Display",
          "MDG_CONDITIONS.MDG_ORDER_TYPE": orderType || "VERP",
          "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || "US",
          "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": taskData?.ATTRIBUTE_5 ? taskData.ATTRIBUTE_5 : workflowGroup ? workflowGroup : "Z_FIN_REQ_INITIATE",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        if (Array.isArray(data?.data?.result) && data?.data?.result.every((item) => Object.keys(item).length !== 0)) {
          let responseData = data?.data?.result[0]?.MDG_INTORD_FIELD_CONFIG;
          
          if (responseData && Array.isArray(responseData)) {
            const { transformedData, mandatoryFields } = transformInternalOrderFieldConfigData(responseData);
            
            // TODO: Dispatch to Internal Order specific Redux actions when available
            // dispatch(setInternalOrderTabs(allTabsData));
            // dispatch(setInternalOrderConfig({ InternalOrder: { allfields: transformedData, mandatoryFields } }));
            
            console.log("Internal Order Field Configuration:", { transformedData, mandatoryFields });
            
            setLoading(false);
            return { transformedData, mandatoryFields };
          }
        } else {
          console.log("No field configuration data received");
        }
        setLoading(false);
      }
    };

    const hError = (error) => {
      customError(error);
      setError(error);
      setLoading(false);
    };

    const url = applicationConfig.environment === "localhost" 
      ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}` 
      : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;
      
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const fetchInternalOrderFieldConfig = (orderType) => {
    try {
      return getInternalOrderFieldConfig(orderType);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    fetchInternalOrderFieldConfig,
  };
};

export default useInternalOrderFieldConfig;
