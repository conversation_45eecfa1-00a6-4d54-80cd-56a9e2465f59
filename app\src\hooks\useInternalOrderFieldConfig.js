import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { groupBy, removeHiddenAndEmptyObjects, transformStructureForAllTabsData } from "@helper/helper";
import { destination_IDM } from "../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, REQUEST_TYPE, TASK_NAME, VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";
import { setInternalOrderFieldConfig } from "../modules/internalOrder/slice/InternalOrderSlice";

const transformInternalOrderFieldConfigData = (responseData) => {
  let mandatoryFields = {};
  let sortedData = responseData?.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);

  const groupedFields = groupBy(sortedData, "MDG_MAT_VIEW_NAME");
  let view_data_array = [];
  Object.entries(groupedFields).forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_MAT_CARD_NAME");
    let cards = [];

    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      cardFields.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);

      let cardDetails = cardFields.map((item) => ({
        fieldName: item.MDG_MAT_UI_FIELD_NAME,
        sequenceNo: item.MDG_MAT_SEQUENCE_NO,
        fieldType: item.MDG_MAT_FIELD_TYPE,
        maxLength: item.MDG_MAT_MAX_LENGTH,
        dataType: item.MDG_MAT_DATA_TYPE,
        viewName: item.MDG_MAT_VIEW_NAME,
        cardName: item.MDG_MAT_CARD_NAME,
        cardSeq: item.MDG_MAT_CARD_SEQUENCE,
        viewSeq: item.MDG_MAT_VIEW_SEQUENCE,
        value: item.MDG_MAT_DEFAULT_VALUE,
        visibility: item.MDG_MAT_VISIBILITY,
        jsonName: item.MDG_MAT_JSON_FIELD_NAME,
      }));

      cards.push({
        cardName,
        cardSeq: cardFields[0].MDG_MAT_CARD_SEQUENCE,
        cardDetails,
      });
    });

    cards.sort((a, b) => a.cardSeq - b.cardSeq);
    view_data_array.push({
      viewName,
      viewSeq: fields[0].MDG_MAT_VIEW_SEQUENCE, // take from first field in the group
      cards,
    });
  });

  // Sort views by viewSeq
  view_data_array.sort((a, b) => a.viewSeq - b.viewSeq);

  let filteredData = removeHiddenAndEmptyObjects(view_data_array);
  let transformedData = {};
  filteredData.forEach((view) => {
    let cardData = {};
    view.cards.forEach((card) => {
      cardData[card.cardName] = card.cardDetails;
      if (view.viewName !== "Request Header") {
        card.cardDetails.forEach((detail) => {
          if (detail.visibility === VISIBILITY_TYPE.MANDATORY) {
            if (!mandatoryFields[detail.viewName]) {
              mandatoryFields[detail.viewName] = [];
            }
            mandatoryFields[detail.viewName].push({ jsonName: detail?.jsonName, fieldName: detail?.fieldName });
          }
        });
      }
    });
    transformedData[view.viewName] = cardData;
  });

  return { transformedData, mandatoryFields };
};

const useInternalOrderFieldConfig = () => {
  const dispatch = useDispatch();
  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state.payload?.payloadData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const userData = useSelector((state) => state.userManagement.userData);
  const fieldConfigByOrderType = useSelector((state) => state.internalOrder?.fieldConfigByOrderType || {});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Helper function to get field config for a specific order type
  const getFieldConfigForOrderType = (orderType) => {
    return fieldConfigByOrderType[orderType] || null;
  };

  const getTemplateData = async (orderType) => {
    setLoading(true);
    console.log("Making DT call for orderType:", orderType);

    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_INTORD_FIELD_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": initialPayload?.RequestType || REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_ORDER_TYPE": orderType || "TUK1",
          "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || "US",
          "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": TASK_NAME.REQ_INITIATE_FIN,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    console.log("DT payload:", payload);

    const hSuccess = (data) => {
      console.log("DT Response received:", data);

      if (data.statusCode === API_CODE.STATUS_200) {
        console.log("Status 200 - checking result data...");

        if (Array.isArray(data?.data?.result) && data?.data?.result.every(item => Object.keys(item).length !== 0)) {
          console.log("Result array is valid, extracting field config...");
          console.log("Full result[0]:", data?.data?.result[0]);
          console.log("Available keys in result[0]:", Object.keys(data?.data?.result[0] || {}));

          let responseData = data?.data?.result[0]?.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE;
          console.log("Raw responseData:", responseData);

          const { transformedData, mandatoryFields } = transformInternalOrderFieldConfigData(responseData);
          console.log("Transformed data:", { transformedData, mandatoryFields });
          let ioTabsData = Object.keys(transformedData);

          const allTabsData = ioTabsData.map((tab) => ({
              tab,
              data: transformedData[tab],
            }));

          // Store field configuration in Redux with orderType as key
          console.log("About to dispatch setInternalOrderFieldConfig with:", {
            orderType: orderType || "TUK1",
            fieldData: {
              rawResponse: responseData,
              transformedData,
              mandatoryFields,
              allTabsData,
              ioTabsData
            }
          });

          dispatch(setInternalOrderFieldConfig({
            orderType: orderType || "TUK1",
            fieldData: {
              rawResponse: responseData,
              transformedData,
              mandatoryFields,
              allTabsData,
              ioTabsData
            }
          }));

          console.log("Internal Order Field Configuration dispatched for orderType:", orderType);
        } else {
          // Store empty data for this order type
          dispatch(setInternalOrderFieldConfig({
            orderType: orderType || "TUK1",
            fieldData: {}
          }));
          console.log("No field configuration data received for orderType:", orderType);
        }
        setLoading(false);
      }
    };

    const hError = (error) => {
      customError(error);
      setError(error);
      setLoading(false);
    };

    const url =
      applicationConfig.environment === "localhost"
        ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`
        : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const fetchInternalOrderFieldConfig = (orderType) => {
    try {
      getTemplateData(orderType);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    fetchInternalOrderFieldConfig,
    getFieldConfigForOrderType,
    fieldConfigByOrderType
  };
};

export default useInternalOrderFieldConfig;
