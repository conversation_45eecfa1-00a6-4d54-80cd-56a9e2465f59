import {
  <PERSON>,
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { ArrowCircleLeftOutlined } from "@mui/icons-material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import {
  button_Outlined,
  button_Primary,
} from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import {
  DECISION_TABLE_NAME,
  <PERSON>IAL<PERSON>UGE_BOX_MESSAGES,
  LOCAL_STORAGE_KEYS,
  MODULE_MAP,
  REGION,
} from "@constant/enum";
import {
  setHeaderFieldsBOM,
  setTabValue,
  clearTabData,
  setSelectedRowID,
  setBomRows,
  setDropDownDataBOM,
  setBOMpayloadData,
  clearBOMpayloadData,
} from "./bomSlice";
import RequestHeaderBOM from "./RequestHeaderBOM";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { resetPayloadData } from "@app/payloadSlice";
import BOMListDetails from "./BOMListDetails";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../destinationVariables";
import { setLocalStorage } from "@helper/helper";

const BomCreateRequest = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getAttachmentDt, dtData: dtAttachmentData } =
    useGenericDtCall();
  const { t } = useLang();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const steps = [
    t("Request Header"),
    t("BOM List"),
    t("Attachments & Remarks"),
    t("Preview"),
  ];
  const requestIdHeader = useSelector((state) => state.bom.requestHeaderID);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const tabValue = useSelector((state) => state.bom.tabValue);
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");

  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if (requestId) {
      // if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.material?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
      //   dispatch(setTabValue(0));
      //   setIsSecondTabEnabled(false);
      //   setIsAttachmentTabEnabled(false);
      // } else {
      //   dispatch(setTabValue(1));
      //   setIsSecondTabEnabled(true);
      //   setIsAttachmentTabEnabled(true);
      // }
      // setAddHardCodeData(true);
    } else {
      dispatch(setTabValue(0));
    }
    fetchHeaderFieldsFromDt();
    dispatch(setDropDownDataBOM({ keyName: "Region", data: REGION }));
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BOM);
    dispatch(clearBOMpayloadData());
    dispatch(clearTabData());
    dispatch(setSelectedRowID(null));
    dispatch(setBomRows([]));
    fetchAndSetDropdown("getBomUsage", "BOMUsage");
    fetchAndSetDropdown("getBomItemCategory", "Category");
  }, []);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBOM(requestHeaderObj));
    }
  }, [dtData]);

  const fetchAndSetDropdown = (endpoint, keyName) => {
    const hSuccess = (data) => {
      dispatch(setDropDownDataBOM({ keyName, data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_BOM}/data/${endpoint}`, "get", hSuccess, hError);
  };

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.BOM);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_MODULE": "BOM",
        },
      ],
    };
    getDtCall(payload);
  };
  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestIdHeader || requestId ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>{requestIdHeader ? requestIdHeader : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
        </Grid>

        {payloadFields?.TemplateName && (
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              textAlign: "left",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Template Name")}: <span>{payloadFields?.TemplateName}</span>
          </Typography>
        )}
        <IconButton
          onClick={() => {
            if (reqBench) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title={t("Back")}
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>
        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {tabValue === 0 && (
          <>
            <RequestHeaderBOM
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
          </>
        )}
        {tabValue === 1 && (
          <BOMListDetails
            setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            setCompleted={setCompleted}
          />
        )}
      </Box>
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={t("Warning")}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {t(DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE)}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default BomCreateRequest;
