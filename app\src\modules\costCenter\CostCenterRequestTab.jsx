import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import RequestHeaderCC from "./RequestHeaderCC";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import RequestDetailsCC from "./RequestDetailsCC";
import { setActiveStep } from "../../app/redux/stepperSlice";
import { doAjax } from "../../components/Common/fetchService";
import {
  Step,
  StepButton,
  Stepper,
  IconButton,
  Box,
  Grid,
  Typography,
} from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { LOCAL_STORAGE_KEYS, REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  destination_CostCenter_Mass,
  destination_IDM,
} from "../../destinationVariables";
import RequestDetailsChangeCC from "./RequestDetailsChangeCC";
import {
  resetCostCenterStateCc,
  setCCPayload,
  setRequestHeaderPayloadData,
} from "../../app/costCenterTabsSlice";
import { resetPayloadData } from "../../app/payloadSlice";
import {
  fetchCurrencyBasedOnCompCode,
  fetchProfitCenter,
  fetchRegionBasedOnCountryCC,
  getCompanyCodeBasedOnControllingArea,
  transformApiResponseToReduxPayloadCc,
} from "../../functions";
import { setRequestHeader } from "../../app/requestDataSlice";
import { setDropDown } from "../../app/dropDownDataSlice";
import ExcelOperationsCard from "../../components/Common/ExcelOperationsCard";
import PreviewPage from "../../components/RequestBench/RequestPages/PreviewPage";
import useLang from "@hooks/useLang";
import { MODULE_MAP } from "../../constant/enum";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import { clearLocalStorageItem, setLocalStorage } from "@helper/helper.js";
import useCostCenterChangeFieldConfig from "@hooks/useCostCenterChangeFieldConfig";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { END_POINTS } from "@constant/apiEndPoints";
import { setDropDown as setDropDownAction, setOdataApiCall } from "@costCenter/slice/costCenterDropDownSlice";

const steps = [
  "Request Header",
  "CostCenter List",
  "Attachments & Comments",
  "Preview",
];
const CostCenterRequestTab = () => {
  const { t } = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const requestHeaderData = useSelector(
    (state) => state.costCenter.payload.requestHeaderData
  );
  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(true);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [isDialogVisible,setisDialogVisible] = useState(false)
  const location = useLocation();
  const navigate = useNavigate();
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const rowData = location.state;
  const queryParams = new URLSearchParams(location.search);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [attachmentsData, setAttachmentsData] = useState([]);
  const reqBench = queryParams.get("reqBench");
  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const onlyDigits = (val) => String(val || "").replace(/\D/g, "");
  const { getChangeTemplate } = useCostCenterChangeFieldConfig(MODULE_MAP.CC);
  const templateFullData = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata || []
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);  const isCostCenterApiCalled = useSelector((state) => state.costCenterDropDownData?.isOdataApiCalled)
  const { fetchAllDropdownFMD } = useDropdownFMDData(destination_CostCenter_Mass,setDropDownAction);

  const handleUploadCC = (file) => {
    let url = "";
    url = "getAllCostCenterFromExcel";
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append(
      "dtName",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "MDG_CC_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT"
    );
    formData.append(
      "version",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "v3"
        : "v6"
    );
    formData.append("requestId", requestId ? requestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/${url}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  useEffect(() => {
    getAttachmentsIDM();
  }, []);
  const getAttachmentsIDM = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": MODULE_MAP.CC,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1, // ensure backend expects number not string
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        const attachmentList =
          data?.data?.result?.[0]?.MDG_ATTACHMENTS_ACTION_TYPE ?? [];
        // Optional: If you need to transform for display
        const templateData = attachmentList.map((element, index) => ({
          id: index,
          attachmentName: element?.MDG_ATTACHMENTS_NAME,
          changeEntryFields: element?.MDG_ATTACH_CHNG_ENT_FIELDS,
        }));
        setAttachmentsData(attachmentList);
      } else {
        console.warn("Unexpected statusCode:", data?.statusCode);
      }
    };
    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };
        const endpoint =
          applicationConfig.environment === "localhost"
            ? END_POINTS.INVOKE_RULES.LOCAL
            : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };
  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };
  const handleDownload = () => {
    setDownloadClicked(true);
  }
  
  useEffect(() => {
    if(!isCostCenterApiCalled){
      fetchAllDropdownFMD("costCenter")
      dispatch(setOdataApiCall(true))
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.CC)
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
    }
  },[])

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);
  const getDisplayDataCC = (requestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available";
    if (reqBench === "true") {
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? onlyDigits(requestId) : "",
        massCreationId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CREATE ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD )
            ? requestId
            : "",
        massChangeId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CHANGE ||
            RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD )
            ? requestId
            : "",
        page: 0,
        size: 10,
      };
      const hSuccess = (response) => {
        const apiResponse = response?.body || [];
        let requestHeaderData = response?.body[0]?.Torequestheaderdata;
        let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;
        let displayData = {
          RequestId: requestHeaderData.RequestId,
          RequestPrefix: requestHeaderData.RequestPrefix,
          ReqCreatedBy: requestHeaderData.ReqCreatedBy,
          ReqCreatedOn: requestHeaderData.ReqCreatedOn,
          ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
          RequestType: requestHeaderData.RequestType,
          RequestDesc: requestHeaderData.RequestDesc,
          RequestStatus: apiResponse?.[0]?.RequestStatus,
          RequestPriority: requestHeaderData.RequestPriority,
          FieldName: requestHeaderData.FieldName,
          TemplateName: requestHeaderData.TemplateName,
          Division: requestHeaderData.Division,
          region: requestHeaderData.region,
          leadingCat: requestHeaderData.leadingCat,
          firstProd: requestHeaderData.firstProd,
          launchDate: requestHeaderData.launchDate,
          isBifurcated: requestHeaderData.isBifurcated,
          screenName: requestHeaderData.screenName,
          TotalIntermediateTasks: TotalIntermediateTasks,
        };
        dispatch(setRequestHeaderPayloadData(displayData));
        dispatch(setRequestHeader(requestHeaderData));
        // dispatch(resetPayloadData(displayData));
        setApiResponses(apiResponse);
        const transformedPayload =
          transformApiResponseToReduxPayloadCc(apiResponse);
        apiResponse.forEach((item) => {
          if (item?.ToCostCenterData?.[0]?.AddrCountry) {
            fetchRegionBasedOnCountryCC(
              item?.ToCostCenterData?.[0]?.AddrCountry,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
          }
          if (item?.ToCostCenterData?.[0]?.CompCode) {
            fetchCurrencyBasedOnCompCode(
              item?.ToCostCenterData?.[0]?.CompCode,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
          }
          if (item?.ControllingArea) {
            fetchProfitCenter(
              item?.ControllingArea,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
          }
          if (item?.Torequestheaderdata?.RequestType) {
            getChangeTemplate();
          }
          if (item?.Torequestheaderdata?.TemplateName) {
            let selectedTemplate = item?.Torequestheaderdata?.TemplateName;
            const filteredAndSortedFields = templateFullData
              .filter(
                (item) =>
                  item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplate &&
                  item?.MDG_MAT_CHANGE_TYPE === "Item" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Display"
              )
              .sort((a, b) => {
                // Convert to number for proper sorting, handle nulls/fallbacks
                const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
                const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
                return seqA - seqB;
              });
            const uniqueFieldNames = [
              ...new Set(
                filteredAndSortedFields
                  .map((item) => item?.MDG_MAT_FIELD_NAME)
                  .filter(Boolean)
              ),
            ].map((field) => ({ code: field }));
            // return uniqueFieldNames;
            //   const changeFieldNames = uniqueFieldNames.map((item) => ({
            //   code: item?.fieldName,
            //   desc: "",
            // }));
            dispatch(
              setDropDown({
                keyName: "FieldName",
                data: uniqueFieldNames || [],
                // keyName2: uniqueId,
              })
            );
          }
        });
        getCompanyCodeBasedOnControllingArea(response?.body?.[0]?.ControllingArea, "ETP")
        dispatch(setCCPayload(transformedPayload?.payload));
      };
      const hError = (error) => {
        console.error("Error fetching CC Create data:", error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/displayMassCostCenterDto`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataCC(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
            RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
        setAddHardCodeData(true);
      } else {
        dispatch(setActiveStep(0));
      }
    };
    loadData();
    return () => {
      dispatch(resetPayloadData());
      dispatch(resetCostCenterStateCc());
      dispatch(setRequestHeader({}));
      dispatch(setDropDown({ keyName: "FieldName", data: [] }));
    };
  }, [requestId, dispatch]);
  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_CC);
    }
  };
  const handleCancel = () => {
    setisDialogVisible(false);
  };
  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestId || requestIdHeader ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>
                {requestIdHeader
                  ? requestHeaderSlice?.requestPrefix +
                    "" +
                    requestHeaderSlice?.requestId
                  : `CCTNEW${requestId}`}
              </span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
        </Grid>
        <IconButton
          onClick={() => {
            if (reqBench === "true") {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>
        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 &&
                    !isSecondTabEnabled &&
                    !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {tabValue === 0 && (
          <>
            <RequestHeaderCC
              apiResponse={apiResponses}
              reqBench={reqBench}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.material?.length) ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperationsCard
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadCC}
                />
              )}
          </>
        )}
        {tabValue === 1 &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" ||
          requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangeCC
              reqBench={reqBench}
              requestId={requestId}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={true}
              setCompleted={setCompleted}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
          ) : (
            <RequestDetailsCC reqBench={reqBench} apiResponses={apiResponses} />
          ))}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestHeaderSlice?.requestPrefix +
                  "" +
                  requestHeaderSlice?.requestId
                : requestId
            }
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage module={MODULE_MAP?.CC} />
          </Box>
        )}
      </Box>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={"Warning"}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </div>
  );
};
export default CostCenterRequestTab;
