import { Box, Button, Checkbox, <PERSON>Field, Typography, IconButton, Tooltip } from "@mui/material";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useState } from "react";
import useLang from "@hooks/useLang";
import { DataGrid } from "@mui/x-data-grid";
import { v4 as uuidv4 } from "uuid";
import { DeleteOutlineOutlined as DeleteOutlineOutlinedIcon, CloseFullscreen as CloseFullscreenIcon, CropFree as CropFreeIcon, FormatColorResetRounded } from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import { setInternalOrderTabs, setInternalOrderConfig, updateModuleFieldDataIO, removeRowDataIO } from "./slice/InternalOrderSlice";
import { groupBy } from "lodash";

// Helper function to transform Internal Order field configuration data
const transformInternalOrderFieldConfigData = (responseData) => {
  let mandatoryFields = {};
  let sortedData = responseData?.sort((a, b) => a.MDG_IO_SEQUENCE_NO - b.MDG_IO_SEQUENCE_NO);

  const groupedFields = groupBy(sortedData, "MDG_IO_VIEW_NAME");
  let view_data_array = [];

  Object.entries(groupedFields).forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_IO_CARD_NAME");
    let cards = [];

    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      let fieldsArray = cardFields.map((field) => {
        if (field.MDG_IO_VISIBILITY === "Mandatory") {
          if (!mandatoryFields[viewName]) {
            mandatoryFields[viewName] = [];
          }
          mandatoryFields[viewName].push(field.MDG_IO_JSON_FIELD_NAME);
        }

        return {
          fieldName: field.MDG_IO_UI_FIELD_NAME,
          jsonName: field.MDG_IO_JSON_FIELD_NAME,
          fieldType: field.MDG_IO_FIELD_TYPE,
          visibility: field.MDG_IO_VISIBILITY,
          maxLength: field.MDG_IO_MAX_LENGTH,
          sequenceNo: field.MDG_IO_SEQUENCE_NO,
          value: field.MDG_IO_DEFAULT_VALUE,
          viewName: viewName,
        };
      });

      cards.push({
        cardName,
        fields: fieldsArray,
      });
    });

    view_data_array.push({
      viewName,
      cards,
    });
  });

  const transformedData = {};
  view_data_array.forEach(view => {
    transformedData[view.viewName] = view;
  });

  return { transformedData, mandatoryFields };
};

const RequestDetailsIO = () => {
  const { t } = useLang();
  const dispatch = useDispatch();

  // State management
  const [rows, setRows] = useState([]);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [page, setPage] = useState(0);
  const IODropdownData = useSelector((state) => state.internalOrder.dropDownDataIO);
  const orderTypeOptions = [{ code: "TUK1", desc: "TUK1" }]; // Dummy dropdown option

  // Function to add a new empty row and store in Redux
  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      internalOrder: "",
      controllingArea: "",
      orderType: "",
      description: "",
      compCode: "",
    };

    // Add to local state
    setRows([...rows, newRow]);

    // Initialize row data in Redux
    Object.entries(newRow).forEach(([fieldName, fieldValue]) => {
      if (fieldName !== 'id') { // Don't store the ID as a field
        dispatch(updateModuleFieldDataIO({
          uniqueId: id,
          keyName: fieldName,
          data: fieldValue,
          viewID: null,
        }));
      }
    });
  };
  const handleCellEdit = async ({ id, field, value }) => {
    // 1. Update local state
    const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    setRows(updatedRows);

    // 2. Store field data in Redux with unique ID
    dispatch(updateModuleFieldDataIO({
      uniqueId: id,
      keyName: field,
      data: value,
      viewID: null, // Will be set based on field configuration
    }));

    // 3. Handle Order Type change - make DT call to get all views and fields
    if (field === "orderType") {
      const changedRow = updatedRows.find((row) => row.id === id);

      // DT call to get field configuration for this order type
      const fieldConfigPayload = {
        decisionTableId: null,
        decisionTableName: "MDG_IO_FIELD_CONFIG",
        version: "v1",
        rulePolicy: null,
        validityDate: null,
        conditions: [
          {
            "MDG_CONDITIONS.MDG_IO_ORDER_TYPE": value,
            "MDG_CONDITIONS.MDG_IO_SCENARIO": "CREATE", // or based on request type
          },
        ],
      };

      try {
        // Get field configuration
        const fieldConfigResponse = await doAjax("POST", END_POINTS.DT_CALL, fieldConfigPayload);

        if (fieldConfigResponse?.data?.statusCode === 200) {
          const responseData = fieldConfigResponse?.data?.result?.[0]?.MDG_IO_FIELD_DETAILS_ACTION_TYPE;

          if (responseData && Array.isArray(responseData)) {
            // Transform and store field configuration
            const { transformedData, mandatoryFields } = transformInternalOrderFieldConfigData(responseData);

            // Store tabs and configuration in Redux
            const ioTabsData = Object.keys(transformedData);
            const allTabsData = ioTabsData.map((tab) => ({
              tab,
              data: transformedData[tab],
            }));

            dispatch(setInternalOrderTabs(allTabsData));
            dispatch(setInternalOrderConfig({
              InternalOrder: {
                allfields: transformedData,
                mandatoryFields
              }
            }));
          }
        }

        // Additional DT call for dependent field values (like description, controlling area)
        const dependentFieldsPayload = {
          decisionTableId: null,
          decisionTableName: "MDG_ORDER_TYPE_CHANGE_DT",
          version: "v1",
          rulePolicy: null,
          validityDate: null,
          conditions: [
            {
              "MDG_CONDITIONS.MDG_ORDER_TYPE": value,
              "MDG_CONDITIONS.MDG_COMP_CODE": changedRow.compCode || "",
            },
          ],
        };

        const dependentResponse = await doAjax("POST", END_POINTS.DT_CALL, dependentFieldsPayload);
        const dtResult = dependentResponse?.data?.result?.[0] || {};

        // Update dependent fields in both local state and Redux
        const dependentFields = {
          description: dtResult.description || changedRow.description,
          controllingArea: dtResult.controllingArea || changedRow.controllingArea,
        };

        // Update local state
        setRows((prevRows) =>
          prevRows.map((row) =>
            row.id === id ? { ...row, ...dependentFields } : row
          )
        );

        // Update Redux for each dependent field
        Object.entries(dependentFields).forEach(([fieldName, fieldValue]) => {
          if (fieldValue !== changedRow[fieldName]) {
            dispatch(updateModuleFieldDataIO({
              uniqueId: id,
              keyName: fieldName,
              data: fieldValue,
              viewID: null,
            }));
          }
        });

      } catch (error) {
        console.error("Failed to fetch DT data for OrderType:", error);
      }
    }
  };

  // const handleCellEdit = (params) => {
  //   const { id, field, value } = params;
  //   const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
  //   setRows(updatedRows);
  // };

  // Function to delete a row and clean up Redux data
  const handleDeleteRow = (rowId) => {
    // Remove from local state
    const updatedRows = rows.filter((row) => row.id !== rowId);
    setRows(updatedRows);

    // Remove from Redux
    dispatch(removeRowDataIO({ uniqueId: rowId }));
  };

  // Toggle grid zoom functionality
  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Simple columns definition
  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "internalOrder",
      headerName: t("Internal Order"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        return (
          <TextField
            value={params.row.internalOrder || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "internalOrder",
                value: newValue,
              })
            }
            size="small"
            fullWidth
            variant="outlined"
            placeholder={t("Enter Internal Order")}
          />
        );
      },
    },
    {
      field: "controllingArea",
      headerName: t("Controlling Area"),
      flex: 0.7,
      editable: FormatColorResetRounded,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.controllingArea || []}
            value={params.row.controllingArea || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "controllingArea",
                value: newValue,
              })
            }
            placeholder={t("Select Controlling Area")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "orderType",
      headerName: t("Order Type"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            // options={IODropdownData?.orderType || []}
            options={orderTypeOptions}
            value={params.row.orderType || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "orderType",
                value: newValue,
              })
            }
            placeholder={t("Select Order Type")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "description",
      headerName: t("Description"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <TextField
          value={params.row.description || ""}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "description",
              value: e.target.value,
            })
          }
          size="small"
          fullWidth
          variant="outlined"
          placeholder={t("Enter Description")}
        />
      ),
    },
    {
      field: "compCode",
      headerName: t("Comp Code"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.compCode || []}
            value={params.row.compCode || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "compCode",
                value: newValue,
              })
            }
            placeholder={t("Select Company Code")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: t("Actions"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Tooltip title={t("Delete Row")}>
          <IconButton onClick={() => handleDeleteRow(params.row.id)} color="error" size="small">
            <DeleteOutlineOutlinedIcon />
          </IconButton>
        </Tooltip>
      ),
    },
  ];

  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}>
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("Internal Order List")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button variant="contained" color="primary" size="small" onClick={handleAddRow}>
                + {t("Add")}
              </Button>
              <Tooltip title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{ zIndex: "1009" }}>
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowsPerPageOptions={[50]}
                onPageChange={(newPage) => handlePageChange(newPage)}
                pagination
                disableSelectionOnClick
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .MuiDataGrid-cell": {
                    padding: "8px",
                  },
                  "& .MuiDataGrid-columnHeaders": {
                    backgroundColor: "#f5f5f5",
                    fontWeight: "bold",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>
    </div>
  );
};

export default RequestDetailsIO;
