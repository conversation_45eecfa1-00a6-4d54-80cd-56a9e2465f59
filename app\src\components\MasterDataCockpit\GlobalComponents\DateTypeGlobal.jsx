import { colors } from "@constant/colors";
import { <PERSON>rid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography } from "@mui/material";
import { DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import ClearIcon from "@mui/icons-material/Clear";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { useSelector } from "react-redux";
import dayjs from "dayjs";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { updateModuleFieldDataCC } from "@app/costCenterTabsSlice";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { CHANGE_LOG_STATUSES, MODULE } from "@constant/enum";
import { updateModule<PERSON>ieldDataIO } from "@InternalOrder/slice/internalOrderSlice";



const DateTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange ,module} = props;
  // const [value, setValue] = useState(field.defaultValue ? new Date(field.defaultValue) : null);

  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);

  const [value, setValue] = useState(null);
  const [showDateError, setShowDateError] = useState(false);

  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const rowsBodyData = useSelector(
        (state) => state.generalLedger.payload?.rowsBodyData || {}
      );
      let requestStatus=rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"]
      console.log(requestStatus,"requestStatusdaaya")

  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload || {});
    const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
      const valueFromPayloadCC = useSelector((state) => state.costCenter.payload || {});
      const valueFromPayloadIO = useSelector((state) => state.internalOrder.IOpayloadData || {});

  const initialFieldValue =
  module === "CostCenter"?
  valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  valueFromPayloadCC?.requestHeaderData?.[field?.jsonName] ??
  field?.value ?? "" :module === "GeneralLedger"?
  valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ??
  field?.value ?? "" : module === MODULE.IO ?
  valueFromPayloadIO?.[field?.jsonName] ?? field?.value ?? "" :
  valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  valueFromPayloadPC?.requestHeaderData?.[field?.jsonName] ??
  field?.value ??
  "";
  const errorFields = useSelector((state) => state.payload?.errorFields || []);

  // useEffect(() => {
  //   if (initialFieldValue) {
  //     setValue(dayjs(initialFieldValue));
  //   } else {
  //     setValue(null);
  //   }
  // }, [initialFieldValue]);

  useEffect(() => {
  if (initialFieldValue) {
    const isValid = dayjs(initialFieldValue, "DD/MM/YYYY", true).isValid();
    const parsed = isValid
      ? dayjs(initialFieldValue, "DD/MM/YYYY")
      : dayjs(initialFieldValue); // fallback to direct parsing

    setValue(parsed);
  } else {
    setValue(null);
  }
}, [initialFieldValue, field?.jsonName, uniqueId]);


  const handleDateChange = (newValue) => {
    if (field?.jsonName) {
      const formattedDate = newValue ? newValue.toISOString() : null;
      const sapFomatDate = `/Date(${Date.parse(formattedDate)})/`;

      if (module == 'CostCenter'){

        dispatch(
          updateModuleFieldDataCC({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );

      }else if(module == 'GeneralLedger'){
        dispatch(
          updateModuleFieldDataGL({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );

         {
          requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: '',
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: formattedDate,
            requestId: initialPayload?.RequestId,
            childRequestId:requestId
          });
    }

      }else if(module === MODULE.IO){
        dispatch(
          updateModuleFieldDataIO({
            keyName: field?.jsonName,
            data: formattedDate,
          })
        );
      }else{

        dispatch(
          updateModuleFieldData({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );
      }

      setValue(newValue);
    }
  };

  const handleClearDate = () => {
    setValue(null);
    if (field?.jsonName) {
      if (module == 'CostCenter'){
        dispatch(
          updateModuleFieldDataCC({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      }else if(module == 'GeneralLedger'){
        dispatch(
          updateModuleFieldDataGL({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      }else if(module === MODULE.IO){
        dispatch(
          updateModuleFieldDataIO({
            keyName: field?.jsonName,
            data: null,
          })
        );
      }else{
        dispatch(
          updateModuleFieldData({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      }
    }
  };

  return (
    <>
     <Grid item md={2}>
      <Stack>
        <>
            <Typography
              variant="body2"
              color={colors.secondary.grey}
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "100%",
              }}
              title={field?.fieldName}
            >
              {field?.fieldName}
              {(field?.visibility === "Required" || field?.visibility === "0") && (
                <span style={{ color: "red" }}>*</span>
              )}
            </Typography>

            <Stack direction="row" spacing={1} alignItems="center">
              <LocalizationProvider dateAdapter={AdapterDayjs} sx={{ flex: 1 }}>
                {/* <DateTimePicker
                  slotProps={{ 
                    textField: { 
                      size: "small",
                      placeholder: disabled ? "" : "Select date",
                      fullWidth: true,
                      sx: {
                        "& .MuiInputBase-root.Mui-disabled": {
                          "& > input": {
                            WebkitTextFillColor: colors.black.dark,
                            color: colors.black.dark,
                          },
                          backgroundColor: colors.hover.light,
                        },
                        width: "100%",
                      },
                    },
                  }}
                  value={value}
                  disabled={disabled}
                  onChange={handleDateChange}
                  // onError={() => errorFields.includes(keyName)}
                  required={field?.visibility === "0" || field?.visibility === "Required"}
                  renderInput={(params) => (
                    <params.TextField
                      {...params}
                      error={errorFields.includes(field?.jsonName)}
                    />
                  )}
                  sx={{ width: "100%" }}
                /> */}
                <DateTimePicker
  value={value}
  onChange={handleDateChange}
  disabled={disabled}
  minDate={dayjs("1900-01-01")}
  maxDate={dayjs("9999-12-31")}
  slotProps={{
    textField: {
      size: "small",
      placeholder: disabled ? "" : "Select date",
      fullWidth: true,
      sx: {
        "& .MuiInputBase-root.Mui-disabled": {
          "& > input": {
            WebkitTextFillColor: colors.black.dark,
            color: colors.black.dark,
          },
          backgroundColor: colors.hover.light,
        },
        width: "100%",
      },
    },
  }}
/>

              </LocalizationProvider>
              
              {value && !disabled && (
                <IconButton
                  size="small" 
                  onClick={handleClearDate}
                  sx={{ 
                    color: colors.secondary.grey,
                    padding: '4px',
                    flexShrink: 0,
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
            </Stack>
          </>
      </Stack>
     </Grid>
    </>
  );
};

export default DateTypeGlobal;