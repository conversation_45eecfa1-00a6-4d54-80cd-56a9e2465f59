import React, { useEffect } from "react";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { container_Padding } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { ToastContainer } from "react-toastify";
import { REQUEST_HEADER_FILED_NAMES, REQUEST_PRIORITY, REQUEST_TYPE_OPTIONS, VISIBILITY_TYPE, MODULE,SUCCESS_MESSAGES } from "@constant/enum";
import { setDropDownDataIO, updateModuleFieldDataIO, setRequestHeaderDTIO, setSavedReqData, setTabValue } from "./slice/internalOrderSlice";
import { doAjax } from "@components/Common/fetchService";
// import { destination_InternalOrder } from "../../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import FilterFieldGlobal from "@components/MasterDataCockpit/FilterFieldGlobal";
import { destination_InternalOrder } from "../../destinationVariables";
import { use } from "react";
import { useSnackbar } from "@hooks/useSnackbar";

const RequestHeaderIO = ({ setIsSecondTabEnabled, setIsAttachmentTabEnabled }) => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isWorkspace = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");

  const requestHeaderFields = useSelector((state) => state.internalOrder.requestHeaderDTIO);
  const payloadFields = useSelector((state) => state.internalOrder.IOpayloadData);
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const userData = useSelector((state) => state.userManagement.userData);
  const { showSnackbar } = useSnackbar();

  dispatch(setDropDownDataIO({ keyName: "RequestPriority", data: REQUEST_PRIORITY }));
  dispatch(
    setDropDownDataIO({
      keyName: REQUEST_HEADER_FILED_NAMES?.REQUEST_TYPE,
      data: REQUEST_TYPE_OPTIONS,
    })
  );
  useEffect(() => {
    if (!isWorkspace && !isreqBench) {
      dispatch(updateModuleFieldDataIO({ keyName: "ReqCreatedBy", data: userData?.user_id }));
      dispatch(updateModuleFieldDataIO({ keyName: "RequestStatus", data: "DRAFT" }));
    }
  }, [dispatch, isWorkspace, isreqBench, userData]);

  const checkAllFieldsFilled = () => {
    const fields = requestHeaderFields[Object.keys(requestHeaderFields)[0]];
    if (!fields?.length) return false;
    return fields.every((field) => field.visibility !== VISIBILITY_TYPE?.MANDATORY || Boolean(payloadFields?.requestHeaderData?.[field.jsonName]));
  };

  const handleButtonClick = async () => {
    const headerData = payloadFields?.requestHeaderData || {};
    const now = new Date().toISOString();

    const payload = {
      RequestId: "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: headerData?.ReqCreatedOn ? new Date(headerData.ReqCreatedOn).toISOString() : now,
      ReqUpdatedOn: now,
      RequestType: headerData?.RequestType || "",
      RequestPriority: headerData?.RequestPriority || "Medium",
      RequestDesc: headerData?.RequestDesc || "",
      RequestStatus: headerData?.RequestStatus || "DRAFT",
      TemplateName: "",
      FieldName: "",
      Region: headerData?.Region || "US",
      IsBifurcated: headerData?.IsBifurcated ?? false,
    };

    const hSuccess = (data) => {
      console.log("Success response:", data);

      // Check for different possible success indicators
      if (data?.status === true || data?.statusCode === 200 || data?.status === "success") {
        showSnackbar(`${t(SUCCESS_MESSAGES.REQUEST_HEADER_CREATED)} ${data?.body?.RequestId || data?.RequestId || ""}`, "success");
        dispatch(setSavedReqData(data.body || data));

        // Enable tabs if the functions are available
        if (setIsAttachmentTabEnabled) setIsAttachmentTabEnabled(true);
        if (setIsSecondTabEnabled) setIsSecondTabEnabled(true);

        // Add a small delay to ensure state updates are processed
        setTimeout(() => {
          console.log("Setting tab value to 1");
          dispatch(setTabValue(1));
        }, 200);
      } else {
        console.error("Success handler called but response indicates failure:", data);
        showSnackbar("Error occurred while saving Request Header", "error");
      }
    };

    const hError = (err) => {
      console.error("Error response:", err);
      showSnackbar("Error occurred while saving Request Header", "error");
    };

    try {
      showSnackbar("Saving request...", "info");
      doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.CREATE_IO_REQUEST}`, "post", hSuccess, hError, payload);
    } catch (error) {
      console.error("Exception during API call:", error);
      showSnackbar("Error occurred while saving Request Header", "error");
    }
  };

  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderFields).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography sx={{ fontSize: "12px", fontWeight: "700", paddingBottom: "10px" }}>{t(key)}</Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterFieldGlobal isHeader={true} key={innerItem.jsonName} field={innerItem} dropDownData={{}} module={MODULE.IO} disabled={isWorkspace || requestHeaderData?.requestId} requestHeader={true} />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && (
              <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
                <Button variant="contained" color="primary" onClick={handleButtonClick} disabled={!checkAllFieldsFilled()}>
                  {t("Save Request Header")}
                </Button>
              </Box>
            )}
            <ToastContainer />
          </Grid>
        ))}
      </Stack>
    </div>
  );
};

export default RequestHeaderIO;
