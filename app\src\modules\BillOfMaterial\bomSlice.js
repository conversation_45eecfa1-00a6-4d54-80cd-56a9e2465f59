import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  bomData: null,
  loading: false,
  error: null,
  tabValue:0,
  headerFieldsBOM: {},
  BOMpayloadData:{},
  requestHeaderID: null,
  bomRows: [],
  selectedRowID: null,
  tabFieldValues: {}, 
  dropDownData: {}, 
};

const bomSlice = createSlice({
  name: "bom",
  initialState,
  reducers: {
    setBomData: (state, action) => {
      state.bomData = action.payload;
    },
    clearHeaderFieldsBOM: (state) => {
      state.headerFieldsBOM = {};
      state.requestHeaderID = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
    setHeaderFieldsBOM: (state, action) => {
      state.headerFieldsBOM = action.payload;
    },
    setRequestHeaderID: (state, action) => {
      state.requestHeaderID = action.payload;
    },
    setBomRows: (state, action) => {
      state.bomRows = action.payload;
    },
    setSelectedRowID: (state, action) => {
      state.selectedRowID = action.payload;
    },
    updateTabFieldValues: (state, action) => {
      const { rowId, viewName, fieldName, value } = action.payload;
      if (!state.tabFieldValues[rowId]) state.tabFieldValues[rowId] = {};
      if (!state.tabFieldValues[rowId][viewName]) state.tabFieldValues[rowId][viewName] = {};
      state.tabFieldValues[rowId][viewName][fieldName] = value;
    },
    setTabRows: (state, action) => {
      const { rowId, viewName, rows } = action.payload;
      if (!state.tabFieldValues[rowId]) state.tabFieldValues[rowId] = {};
      state.tabFieldValues[rowId][viewName] = rows;
    },
    setDropDownDataBOM: (state, action) => {
      state.dropDownData[action.payload.keyName] = action.payload.data;
      return state;
    },
    clearTabData: (state, action) => {
      const rowId = action.payload;
      if (rowId) {
        delete state.tabFieldValues[rowId];
      } else {
        state.tabFieldValues = {};
      }
    },
    setBOMpayloadData: (state, action) => {
      state.BOMpayloadData[action.payload.keyName] = action.payload.data;
      return state;
    },
    clearBOMpayloadData: (state, action) => {
      state.BOMpayloadData = {};
      return state;
    },
  },
});

export const { 
  setBomData, 
  clearHeaderFieldsBOM, 
  setLoading, 
  setError, 
  clearError, 
  setTabValue, 
  setHeaderFieldsBOM, 
  setRequestHeaderID, 
  setBomRows,
  setDropDownDataBOM,
  setSelectedRowID,
  updateTabFieldValues,
  setTabRows,
  clearTabData,
  setBOMpayloadData,
  clearBOMpayloadData,
} = bomSlice.actions;

export default bomSlice.reducer; 