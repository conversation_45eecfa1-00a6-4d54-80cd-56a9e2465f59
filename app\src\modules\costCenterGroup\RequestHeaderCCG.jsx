import React, { useState, useEffect } from "react";
import { <PERSON>rid, Button, Typography, Stack, Box, } from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "@components/Common/fetchService";
import { container_Padding } from "@components/Common/commonStyles";
import FilterFieldGlobal from "@components/MasterDataCockpit/FilterFieldGlobal";
import { destination_CostCenter_Mass } from "../../destinationVariables";
import { MODULE_MAP, REQUEST_TYPE, VISIBILITY_TYPE, ERROR_MESSAGES, SUCCESS_MESSAGES, MODULE } from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { setRequestHeaderData } from "@app/redux/requestHeaderSlice";
import DownloadDialog from "@components/Common/DownloadDialog";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useLocation, useNavigate } from "react-router-dom";
import usePCGRequestHeaderConfig from "@hooks/usePCGRequestHeaderConfig";
import { setRequestHeader } from "@app/requestDataSlice";
import ReusableDialogForHierarchy from "@components/Common/ReusableDialogForHierarchy";
import { Hierarchy_Templates } from "@constant/changeTemplates";
import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";
import { MANDATORY_FILTERS } from "@constant/changeTemplates";
import useDownloadExcel from "@hooks/useDownloadExcel";
import { useSnackbar } from "@hooks/useSnackbar";
import { END_POINTS } from "@constant/apiEndPoints";

const RequestHeaderCCG = ({
  downloadClicked,
  setDownloadClicked,
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
}) => {
  const dispatch = useDispatch();
  const payloadFields = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const userData = useSelector((state) => state.userManagement.userData);
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  const fieldNames = useSelector(
    (state) => state.AllDropDown.dropDown.FieldName || []
  );

  const dropDownDataFromRedux = useSelector(
    (state) => state.AllDropDown.dropDown
  );
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const RequestId = queryParams.get("RequestId");
  const isWorkspace = queryParams.get("RequestId");
  const currentDate = `/Date(${Date.now()})/`;
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const { getRequestHeaderTemplatePCG } = usePCGRequestHeaderConfig();
  const { handleDownload, handleEmailDownload } = useDownloadExcel(MODULE?.CCG);
  const { showSnackbar } = useSnackbar();
  const rowData = location.state;
  const requestTypeData = [
    {
      code: "Create",
      tooltip: "Create New Cost Center Group Directly in Application",
    },
    {
      code: "Change",
      tooltip: "Modify Existing Cost Center Group Directly in Application",
    },
    {
      code: "Create with Upload",
      tooltip: "Create New Cost Center Group with Excel Upload",
    },
    {
      code: "Change with Upload",
      tooltip: "Modify Existing Cost Center Group with Excel Upload",
    },
  ];

  const templateNames = [
    { code: "All Other Fields", desc: "" },
    { code: "Address Change", desc: "" },
    { code: "Block", desc: "" },
    { code: "Temporary Block/Unblock", desc: "" },
  ];

  const requestPriority = [
    { code: "High", desc: "" },
    { code: "Medium", desc: "" },
    { code: "Low", desc: "" },
  ];

  dispatch(setRequestHeaderPayloadDataPCG({ keyName: "RequestStatus", data: "DRAFT" }));
  dispatch(setRequestHeaderPayloadDataPCG({keyName: "ReqCreatedBy", data: userData?.user_id}));

  useEffect(() => {
    if (downloadClicked) {
      if (payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
    }
  }, [downloadClicked]);

  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (
      payloadFields &&
      requestHeaderDetails[Object.keys(requestHeaderDetails)]?.length
    ) {
      requestHeaderDetails[Object.keys(requestHeaderDetails)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.visibility === VISIBILITY_TYPE?.MANDATORY
          ) {
            allFilled = false;
          }
        }
      );
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  useEffect(() => {
    getRequestHeaderTemplatePCG();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    if (RequestId && (RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CHANGE) && !rowData?.parentNode?.length) {
      setDialogOpen(true);
    }
  }, [RequestId]);

  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const fieldNameValue =
      fieldNames.map((item) => item.code || "").join(", ") || "";
    setDialogOpen(false);

    const payload = {
      RequestId: "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestPrefix: "",
      RequestPriority: payloadFields?.RequestPriority || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: "DRAFT",
      FirstProd: "",
      LaunchDate: "",
      LeadingCat: "",
      Division: "",
      TemplateName: "",
      FieldName: "",
      Region: "",
      FilterDetails: "",
      IsBifurcated: true,
      IsHierarchyGroup: true,
    };

    const hSuccess = (data) => {
      showSnackbar(
        `${SUCCESS_MESSAGES?.SUCCESS_REQUEST_HEADER}${data?.body?.requestPrefix}${data?.body?.requestId}`,
        "success"
      );
      setIsLoading(false);
      setIsAttachmentTabEnabled(true);
      dispatch(setRequestHeaderData(data?.body));
      dispatch(setRequestHeader(data.body));
      if (
        payloadFields?.RequestType === REQUEST_TYPE.CREATE ||
        payloadFields?.RequestType === REQUEST_TYPE.CHANGE
      ) {
        setDialogOpen(true);
        return;
      }

      if (
        payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        setOpenDownloadDialog(true);
        return;
      }
    };

    const hError = (error) => {
      showSnackbar(ERROR_MESSAGES?.ERROR_REQUEST_HEADER, "error");
    };
    doAjax(
      `/${destination_CostCenter_Mass}${END_POINTS?.MASS_ACTION?.CREATE_BOM_REQUEST}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if (!isWorkspace) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.CCG);
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.CCG);
      handleDownloadDialogClose();
    }
  };

  let INITIAL_DIALOG_SCENARIO = {
    [REQUEST_TYPE?.CREATE]: "CREATE_CCG",
    [REQUEST_TYPE?.CHANGE]: "CHANGE_CCG",
  };
  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderDetails).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {key}
            </Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterFieldGlobal
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={{
                        RequestType: requestTypeData,
                        RequestPriority: requestPriority,
                        TemplateName: templateNames,
                      }}
                      disabled={isWorkspace || !!requestHeaderData?.requestId}
                      requestHeader={true}
                      module={"CCG"}
                    />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "20px",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  disabled={!checkAllFieldsFilled()}
                  onClick={handleButtonClick}
                >
                  Save Request Header
                </Button>
              </Box>
            )}
          </Grid>
        ))}
        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />

        {dialogOpen && (
          <ReusableDialogForHierarchy
            open={dialogOpen}
            onClose={() => {
              setDialogOpen(false);
            }}
            parameters={
              Hierarchy_Templates[
                INITIAL_DIALOG_SCENARIO?.[payloadFields?.RequestType]
              ]
            }
            setShowTable={false}
            allDropDownData={dropDownDataFromRedux}
            setIsSecondTabEnabled={setIsSecondTabEnabled}
            module={MODULE?.CCG}
            mandatoryFields={
              MANDATORY_FILTERS?.[
                INITIAL_DIALOG_SCENARIO?.[payloadFields?.RequestType]
              ]
            }
          />
        )}
        <DownloadDialog
          onDownloadTypeChange={onDownloadTypeChange}
          open={openDownloadDialog}
          downloadType={downloadType}
          handleDownloadTypeChange={handleDownloadTypeChange}
          onClose={handleDownloadDialogClose}
        />
      </Stack>
    </div>
  );
};

export default RequestHeaderCCG;
