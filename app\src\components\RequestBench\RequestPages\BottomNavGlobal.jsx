import React, { useState } from "react";
import {
  BottomNavigation,
  Box,
  Button,
  Paper,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Typography,
  FormControl,
  FormHelperText,
  Tooltip,
} from "@mui/material";
import { button_Primary } from "../../common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import ReusableBackDrop from "../../Common/ReusableBackDrop";
import {
  MODULE,
  MODULE_MAP,
  REQUEST_STATUS,
  VISIBILITY_TYPE,
} from "../../../constant/enum";
import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

const BottomNavGlobal = ({
  handleSaveAsDraft,
  handleSubmitForReview,
  handleSubmitForApprove,
  handleSendBack,
  handleCorrection,
  handleRejectAndCancel,
  handleValidateAndSyndicate,
  validateAllRows,
  isSaveAsDraftEnabled = true,
  filteredButtons,
  validateEnabled = true,
  moduleName,
  isHierarchy=false,
}) => {
  console.log("filteredButtons", filteredButtons);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [isMaxLength, setIsMaxLength] = useState(false);
  const [buttonName, setButtonName] = useState("");
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationMessage, setConfirmationMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const maxLength = 200;

  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogMessage, setDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("success");
  const [userInput, setUserInput] = useState("");
  const [remarksError, setRemarksError] = useState(false);
  const [isMandatory, setIsMandatory] = useState(false);
  const [inputText, setTextInput] = useState(false);
  const [currentActionType, setCurrentActionType] = useState("");
  const [currentButtonState, setCurrentButtonState] = useState(null);
  const [isTable, setIsTable] = useState(false);
  const [tableRows, setTableRows] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const requestBenchData = location.state;
  console.log("requestBenchData", requestBenchData);

  const costCenterData = useSelector(
    (state) => state?.costCenter?.payload?.rowsBodyData
  );
  const profitCenterData = useSelector(
    (state) => state?.profitCenter?.payload?.rowsBodyData
  );
  const generalLedgerData = useSelector(
    (state) => state?.generalLedger?.payload?.rowsBodyData
  );
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);

  const payloadData = useSelector((state) => {
    switch (moduleName) {
      case MODULE_MAP?.CC:
        return state?.costCenter?.payload?.rowsBodyData;
      case MODULE_MAP?.PC:
        return state?.profitCenter?.payload?.rowsBodyData;
      case MODULE_MAP?.GL:
        return state?.generalLedger?.payload?.rowsBodyData;
      case MODULE_MAP?.CCG:
        return state.hierarchyData;
      case MODULE_MAP?.PCG:
        return state.hierarchyData;
      case MODULE_MAP?.CEG:
        return state.hierarchyData;
      default:
        return null; // or some default value
    }
  });

  const requestStatus = isHierarchy ? payloadData?.RequestStatus : payloadData?.[0]?.RequestStatus;
  const isButtonEnabled = requestBenchData?.reqStatus === "Validated-Requestor";
console.log('requestStatus',requestStatus)
  const handleOpenRemarkDialog = () => {
    setOpenRemarkDialog(true);
  };

  const handleRemarksDialogClose = () => {
    setRemarks("");
    setOpenRemarkDialog(false);
  };

  const handleRemarks = (e) => {
    const newValue = e.target.value;
    setIsMaxLength(newValue.length >= maxLength);

    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      let remarksUpperCase = newValue;
      setRemarks(remarksUpperCase);
    }
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleConfirmDialogOpen = () => {
    setConfirmationDialogOpen(true);
  };

  const handleConfirmDialogClose = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmOk = () => {
    handleConfirmDialogClose();
    if (buttonName === "SAVE_AS_DRAFT") {
      handleSaveAsDraft("SAVE_AS_DRAFT");
    }
  };

  // Dynamic button handlers
  const handleButtonAction = (button) => {
    setMessageDialogMessage("");
    setMessageDialogSeverity("success");
    setCurrentButtonState(button);
    setDialogTitle(button.MDG_DYN_BTN_COMMENT_BOX_NAME);
    setIsMandatory(
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY
    );
    setTextInput(
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY ||
        button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Optional"
    );
    setCurrentActionType(button.MDG_DYN_BTN_ACTION_TYPE);

    if (
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY ||
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Optional"
    ) {
      handleMessageDialogClickOpen();
    } else {
      executeAction(button.MDG_DYN_BTN_ACTION_TYPE, button);
    }
  };

  const executeAction = (actionType, button) => {
    switch (actionType) {
      case "handleSubmitForApproval":
        handleSubmitForApprove();
        break;
      case "handleSubmitForReview":
        handleSubmitForReview();
        break;
      case "handleValidate":
        handleValidateAndSyndicate("VALIDATE");
        break;
      case "handleSAPSyndication":
        handleValidateAndSyndicate("SYNDICATE");
        break;
      case "handleSendBack":
        handleSendBack();
        break;
      case "handleCorrection":
        handleCorrection();
        break;
      case "handleReject":
        handleRejectAndCancel();
        break;
      case "handleDraft":
        handleSaveAsDraft("SAVE_AS_DRAFT");
        break;
      default:
        console.log("Unknown action type");
    }
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setUserInput("");
    setRemarksError(false);
    setIsMandatory(false);
  };

  const handleDialogConfirm = () => {
    if (isMandatory && !userInput) {
      setRemarksError(true);
      return;
    } else {
      handleMessageDialogClose();
      executeAction(currentActionType, currentButtonState);
    }
  };

  const handleHardcodedButtonClick = (action) => {
    setButtonName(action);
    if (action === "SAVE_AS_DRAFT") {
      setConfirmationMessage("Are you sure you want to save as draft?");
      handleConfirmDialogOpen();
    } else if (action === "VALIDATE") {
      if (moduleName === MODULE_MAP?.PCG || moduleName === MODULE_MAP?.CCG) {
        handleValidateAndSyndicate("VALIDATE");
      } else validateAllRows();
    }
    // else if (action === "VALIDATE") {
    //   setConfirmationMessage("Are you sure you want to validate?");
    //   handleConfirmDialogOpen();
    // }
    else {
      handleOpenRemarkDialog();
    }
  };

  const handleActionWithRemarks = () => {
    handleRemarksDialogClose();
    if (buttonName === "SUBMIT_FOR_REVIEW") {
      handleSubmitForReview(buttonName, remarks);
    } else if (buttonName === "APPROVE") {
      handleSubmitForApprove(remarks);
    }
  };

  return (
    <Stack>
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
        elevation={2}
      >
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Box sx={{ display: "flex", gap: 1 }}>
            {!isrequestId ||
            requestBenchData?.reqStatus === REQUEST_STATUS?.DRAFT ||
            requestBenchData?.reqStatus === "Validated-Requestor" ||
            requestBenchData?.reqStatus === "Validation Failed-Requestor" ||
            requestBenchData?.reqStatus === "Upload Successful" ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => handleHardcodedButtonClick("SAVE_AS_DRAFT")}
                  // disabled={!isButtonEnabled}
                >
                  Save as Draft
                </Button>
                <Tooltip title="Simulates in SAP" arrow>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleHardcodedButtonClick("VALIDATE")}
                    disabled={!validateEnabled}
                  >
                    Validate
                  </Button>
                </Tooltip>

                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    setButtonName("SUBMIT_FOR_REVIEW");
                    handleOpenRemarkDialog();
                  }}
                  //disabled={false}
                  disabled={!isButtonEnabled}
                >
                  Submit
                </Button>
                {/* <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    setButtonName("APPROVE");
                    handleOpenRemarkDialog();
                  }}
                >
                  Approve
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => handleHardcodedButtonClick("VALIDATE")}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => handleValidateAndSyndicate("syndicate")}
                >
                  SAP Syndication
                </Button> */}
              </>
            ) : isrequestId && !requestBenchData ? (
              filteredButtons?.map((button, index) => {
                const {
                  MDG_DYN_BTN_BUTTON_NAME: name,
                  MDG_DYN_BTN_BUTTON_STATUS: status,
                } = button;
                const isSyndicationButton = name === "SAP Syndication";

                const isSyndicationEnabled =
                  requestStatus === "Validated-MDM" ||
                  requestStatus === "Validated-Requestor";

                let isButtonDisabled = status === "DISABLED";
                if (isSyndicationButton && isSyndicationEnabled) {
                  isButtonDisabled = false;
                }

             

                return (
                  <Button
                    key={index}
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    disabled={isButtonDisabled}
                    onClick={() => handleButtonAction(button)}
                  >
                    {button.MDG_DYN_BTN_BUTTON_NAME}
                  </Button>
                );
              })
            ) : (
              <></>
            )}
          </Box>
        </BottomNavigation>
      </Paper>

      {/* Remarks Dialog */}
      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openRemarkDialog}
        onClose={handleRemarksDialogClose}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6">
            {buttonName === "SAVE" ? "Save As Draft" : "Remarks"}
          </Typography>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleRemarksDialogClose}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          {buttonName !== "SAVE" ? (
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{
                      backgroundColor: "#F5F5F5",
                      "& .MuiOutlinedInput-root": {
                        "& fieldset": {
                          borderColor: isMaxLength
                            ? "red"
                            : "rgba(0, 0, 0, 0.23)",
                        },
                        "&:hover fieldset": {
                          borderColor: isMaxLength
                            ? "red"
                            : "rgba(0, 0, 0, 0.23)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: isMaxLength ? "red" : "primary",
                        },
                      },
                    }}
                    multiline
                    rows={4}
                    value={remarks}
                    onChange={handleRemarks}
                    placeholder="Enter your remarks here"
                    inputProps={{ maxLength: maxLength }}
                  />
                  <FormHelperText error={isMaxLength}>
                    {isMaxLength
                      ? `Maximum ${maxLength} characters allowed`
                      : `${remarks.length}/${maxLength}`}
                  </FormHelperText>
                </FormControl>
              </Box>
            </Stack>
          ) : (
            <Typography>Are you sure you want to save as draft?</Typography>
          )}
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleRemarksDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleActionWithRemarks}
            variant="contained"
          >
            {buttonName === "SAVE" ? "Yes" : "Submit"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirmation Dialog - styled like ReusableDialog */}
      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={confirmationDialogOpen}
        onClose={handleConfirmDialogClose}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6">Confirmation</Typography>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleConfirmDialogClose}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: "1rem" }}>
          <Typography>{confirmationMessage}</Typography>
        </DialogContent>
        <DialogActions
          sx={{ display: "flex", justifyContent: "end", padding: ".5rem 1rem" }}
        >
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleConfirmDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleConfirmOk}
            variant="contained"
          >
            Yes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reusable Dialog for dynamic buttons */}
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={dialogTitle}
        dialogMessage={dialogMessage}
        handleDialogConfirm={handleDialogConfirm}
        dialogOkText="OK"
        dialogSeverity={messageDialogSeverity}
        showCancelButton={true}
        showInputText={inputText}
        inputText={userInput}
        setInputText={setUserInput}
        mandatoryTextInput={isMandatory}
        remarksError={remarksError}
        isTable={isTable}
        tableColumns={tableColumns}
        tableRows={tableRows}
      />

      {/* Snackbar and Backdrop */}
      {messageDialogMessage && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </Stack>
  );
};

export default BottomNavGlobal;
