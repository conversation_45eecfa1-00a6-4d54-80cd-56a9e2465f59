import { lazy } from 'react';
import { Route } from 'react-router-dom';

const MaterialMaster = lazy(() => import('@material/MaterialMaster'));
const MassMaterialTable = lazy(() => import('../../components/MasterDataCockpit/MassMaterialTable'));
const AdditionalData = lazy(() => import('@material/AdditionalData/AdditionalData'));
const DisplayAdditionalData = lazy(() => import('../../components/MasterDataCockpit/DisplayAdditionalData'));
const ExtendMaterial = lazy(() => import('../../components/MasterDataCockpit/ExtendMaterial'));
const DisplayMaterialSAPView = lazy(() => import('@material/DisplayMaterialSAPView'));
const EditMultipleMaterial = lazy(() => import('../../components/MasterDataCockpit/EditMultipleMaterial'));
const DisplayMultipleMaterialRequestBench = lazy(() => import('../../components/MasterDataCockpit/DisplayMultipleMaterialRequestBench'));
const CostCenter = lazy(() => import('@costCenter/CostCenter'));
const ProfitCenter = lazy(() => import('@profitCenter/ProfitCenter'));
const GeneralLedger = lazy(() => import('@generalLedger/GeneralLedger'));
const BillOfMaterial = lazy(() => import('@BillOfMaterial/BillOfMaterial'));
const InternalOrder = lazy(() => import('@InternalOrder/InternalOrder'));
const ProfitCenterGroup = lazy(() => import('@profitCenterGroup/ProfitCenterGroup'));
const CreateRequestforIO = lazy(() => import('@InternalOrder/CreateRequestforIO'));
const CostCenterGroup = lazy(() => import('@costCenterGroup/CostCenterGroup'));

export const MasterDataRoutes = [
  <Route path="/masterDataCockpit/materialMaster/material" element={<MaterialMaster />} />,
  <Route path="/masterDataCockpit/materialMaster/massMaterialTable" element={<MassMaterialTable />} />,
  <Route path="/masterDataCockpit/materialMaster/createMaterialDetail/additionalData" element={<AdditionalData />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/displayAdditionalData" element={<DisplayAdditionalData />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/extend/:reqId" element={<ExtendMaterial />} />,
  <Route path="/masterDataCockpit/materialMaster/DisplayMaterialSAPView/:matNo" element={<DisplayMaterialSAPView />} />,
  <Route path="/masterDataCockpit/materialMaster/editMultipleMaterial/:description" element={<EditMultipleMaterial />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMultipleMaterialRequestBench/:description" element={<DisplayMultipleMaterialRequestBench />} />,
  <Route path="/masterDataCockpit/costCenter" element={<CostCenter />} />,
  <Route path="/masterDataCockpit/billOfMaterial" element={<BillOfMaterial />} />,
  <Route path="/masterDataCockpit/profitCenter" element={<ProfitCenter />} />,
  <Route path="/masterDataCockpit/generalLedger" element={<GeneralLedger />} />,
  <Route path="/masterDataCockpit/InternalOrder" element={<InternalOrder />} />,
  <Route path="/masterDataCockpit/groupNode/hierarchyNodeProfitCenter" element={<ProfitCenterGroup />} />,
  <Route path="/masterDataCockpit/InternalOrder/createRequestforInternalOrder" element={<CreateRequestforIO />} />,
  <Route path="/masterDataCockpit/groupNode/hierarchyNodeCostCenter" element={<CostCenterGroup />} />,
];