import { useState } from "react";
import AttachmentDialog from "./AttachmentDialog";
import ReusablePromptBox from "../ReusablePromptBox/ReusablePromptBox";
import { useNavigate } from "react-router-dom";
import { Stack } from "@mui/material";

const ReusableAttachementAndComments = ({
  title = "",
  module = "",
  artifactName = "",
  getAttachments=()=>{},
  artifactId = "",
  poNumber = "",
  processName = "",
  isAnAttachment = "",
  commentOnly = true,
  view = false,
  attachmentType = "",
  requestId = "",
  disableCheck,
}) => {
  const navigate = useNavigate();
  //<-- Functions and variables for ReusablePromptBox *promptAction_Functikons -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
    okButtonText: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: true,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE,WARNING,QUANTITYERROR,
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      navigate("/purchaseOrder/management");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return () => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        case "COMMENTERROR":
        default:
          return (value) => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        default:
          return () => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  return (
    <>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      <Stack>
        {commentOnly && (
          <AttachmentDialog
          sidebyside={true}
            view={view}
            useMetaData={false}
            title={title}
            artifactName={artifactName}
            artifactId={artifactId}
            processName={processName}
            poNumber={poNumber}
            promptAction_Functions={promptAction_Functions}
            isAnAttachment={isAnAttachment}
            attachmentType={attachmentType}
            requestId={requestId}
            module={module}
            getAttachmentshook={getAttachments}
          />
        )}
      </Stack>
    </>
  );
};

export default ReusableAttachementAndComments;