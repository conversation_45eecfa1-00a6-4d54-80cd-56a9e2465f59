import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { DeleteForeverOutlined, ErrorOutline, Refresh, RocketLaunchOutlined, TrackChangesOutlined } from "@mui/icons-material";
import PreviewIcon from "@mui/icons-material/Preview";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  setChangeFieldRows,
  setChangeFieldRowsDisplay,
  setIsSubmitDisabled,
} from "@app/payloadSlice";
import { clearPaginationData } from "@app/paginationSlice";
import SyncIcon from '@mui/icons-material/Sync';
import InfoIcon from "@mui/icons-material/Info";
import useLogger from "@hooks/useLogger";
import { Checkbox, Grid, Paper, IconButton, Typography, TextField, Box, Tooltip, Accordion, AccordionSummary, AccordionDetails, Chip, ListItemText, tooltipClasses, Autocomplete, FormControlLabel, FormGroup, CircularProgress, Popover, Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions, BottomNavigation, ButtonGroup, Popper, ClickAwayListener, MenuList, Divider, Button } from "@mui/material";
import { Workflow } from "@cw/rds/icons";
import useGenericDtCall from "@hooks/useGenericDtCall";
import moment from "moment/moment";
import SelectionSummary from "./SelectionSummary";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice";
import html2canvas from "html2canvas";
import { v4 as uuidv4 } from "uuid";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import { button_Outlined, button_Primary, font_Small, outerContainer_Information, outermostContainer, outermostContainer_Information, iconButton_SpacingSmall,button_Marginleft } from "../Common/commonStyles";
import { LocalizationProvider,DatePicker,
  DesktopDatePicker, } from "@mui/x-date-pickers";
import DateRange from "../Common/DateRangePicker";
import {
  destination_Admin, destination_BankKey,
  destination_CostCenter_Mass,
 
  destination_GeneralLedger_Mass,
  destination_MaterialMgmt,
  destination_ProfitCenter,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { doAjax, promiseAjax } from "../Common/fetchService";
import { initialDataUpdate, setIwmMyTask } from "../../app/initialDataSlice";
import ReusableTable from "../Common/ReusableTable";
import {
  accountingDataTabs,
  basicDataTabs,
  mrpDataTabs,
  purchasingDataTabs,
  salesDataTabs,
} from "../../app/tabsDetailsSlice";
import { setDropDown } from "../../app/dropDownDataSlice";
import { setProfitCenterAddressTab, setProfitCenterBasicDataTab, setProfitCenterCommunicationTab, setProfitCenterCompCodesTab, setProfitCenterHistoryTab, setProfitCenterIndicatorsTab } from "../../app/profitCenterTabsSlice";
import { setCostCenterAddressTab, setCostCenterBasicDataTab, setCostCenterCommunicationTab, setCostCenterControlTab, setCostCenterHistoryTab, setCostCenterTemplatesTab } from "../../app/costCenterTabsSlice";
import { commonSearchBarClear,commonSearchBarUpdate } from "../../app/commonSearchBarSlice";
import { setTaskData } from "../../app/userManagementSlice";
import EmailIcon from "@mui/icons-material/Email";
import {
  checkIwaAccess,
  exportAsPDF,
  saveExcel,
  savePDF,
} from "../../functions";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ReusableDialogForAllData from "../Common/ReusableDialogForAllData";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import AccessTimeFilledRoundedIcon from "@mui/icons-material/AccessTimeFilledRounded";
import { attachments } from "../ConfigCockpit/UserManagement/Data/data";
import jsPDF from "jspdf";
import CustomDialog from "@components/Common/ui/CustomDialog";
import {
  API_CODE,
  DELETE_MODAL_BUTTONS_NAME,
  DIALOUGE_BOX_MESSAGES,
 
  REQUEST_STATUS,
  ROLES,
  ERROR_MESSAGES, DECISION_TABLE_NAME, VISIBILITY_TYPE, COLUMN_FIELD_TYPES, SEARCH_FIELD_TYPES,
  MODULE_MAP,
} from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import FilterListIcon from "@mui/icons-material/FilterList";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import BifurcationPopup from "@components/Common/BifurcationPopup";
import useLang from "@hooks/useLang";
import ReusableIcon from "../Common/ReusableIcon";
import CloseIcon from "@mui/icons-material/Close";
import CustomDataGrid from "@components/MasterDataCockpit/ScheduleSyndication/CustomGrid";
import ScheduleIcon from '@mui/icons-material/Schedule';
import Switch from "@mui/material/Switch";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { setLocalStorage,filterNavigation, shouldShowChildRequestHistoryButton, shouldShowChildCancelButton, shouldShowChildErrorReportButton, shouldShowParentCancelButtonParent, shouldShowParentErrorReportButtonParent, shouldShowViewChildButton } from "@helper/helper";
import { LOCAL_STORAGE_KEYS } from "@constant/enum";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: "8px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&:before": {
    display: "none",
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: "8px 8px 0 0",
  transition: "all 0.2s ease-in-out",
  "&:hover": {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const StyledTextField = styled(TextField)({
  "& .MuiOutlinedInput-root": {
    borderRadius: "4px",
    "&:hover fieldset": {
      borderColor: colors.primary.main,
    },
  },
});

const FilterContainer = styled(Grid)({
  padding: "0.75rem",
  gap: "0.5rem",
});

const ButtonContainer = styled(Grid)({
  display: "flex",
  justifyContent: "flex-end",
  paddingRight: "0.75rem",
  paddingBottom: "0.75rem",
  paddingTop: "0rem",
  gap: "0.5rem",
});

const ActionButton = styled(Button)({
  borderRadius: "4px",
  padding: "4px 12px",
  textTransform: "none",
  fontSize: "0.875rem",
});

const LabelTypography = styled(Typography)({
  fontSize: "0.75rem",
  color: colors.primary.dark,
  marginBottom: "0.25rem",
  fontWeight: 500,
});

const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const exportAsPicture = () => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("BODY")[0];
  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data.scrollWidth - data.clientWidth;

  if (newWidth > data.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  html.style.width = htmlWidth + "px";
  body.style.width = bodyWidth + "px";

  html2canvas(data)
    .then((canvas) => {
      return canvas.toDataURL("image/png", 1.0);
    })
    .then((image) => {
      saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
      html.style.width = null;
      body.style.width = null;
    });
};

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};

export let getFilter;

const RequestBench = () => {
  const appSettings = useSelector((state) => state.appSettings);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  let userData = useSelector((state) => state.userManagement.userData);
  let userRoles = useSelector((state) => state.userManagement.roles);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [clearClicked, setClearClicked] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [currentModule, setCurrentModule] = useState("");
  const { t } = useLang();

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { customError } = useLogger();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterial
  );
  const handleOnClick = (materialNumber) => {
    // setViewDetailpage(true);
    //     dispatch(setHistoryPath({url:window.location.pathname, module:"po workbench"}));

    navigate(
      "/masterDataCockpit/RequestBench/displayMaterialDetail/" + materialNumber
    );
  };

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));

  const [isLoading, setIsLoading] = useState(true);
  const [openPopup, setOpenPopup] = useState(false);
  const [apiData,setApiData]=useState([])
  const [selectedReqType, setSelectedReqType] = useState([]);
  const [selectedDivision, setSelectedDivision] = useState([]);
  const [selectedCreatedBy, setSelectedCreatedBy] = useState([]);
  const [createdByOptions, setCreatedByOptions] = useState([]);
  const [isLoadingCustom, setIsLoadingCustom] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState([]);
  const [materialOptions, setMaterialOptions] = useState([]);
  const [matInputValue, setMatInputValue] = useState("");
  const [timerId, setTimerId] = useState(null);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [DivisionDropdownLoading,setDivisionDropDownLoading] = useState(false)
  const [value, setValue] = useState(null);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [isChildView, setIsChildView] = useState(false);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [reqStatus, setReqStatus] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [opendialog, setOpendialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [requestBenchType, setRequestBenchType] = useState("Material");
  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const [initialLoad, setinitialLoad] = useState(true);
  const [openAlldataDialog, setOpenAllDataDialog] = useState(false);
  const priortyOptions = ["Low", "Medium", "High"];
  const [displayData, setDisplayData] = useState({});
  const [costCenterCount, setCostCenterCount] = useState([]);
  const [profitCenterCount, setProfitCenterCount] = useState([]);
  const [bankKeyCount, setBankKeyCount] = useState([]);
  const [glCount, setGlCount] = useState([]);
  const [ccgCount, setCCGCount] = useState([]);
  const [pcgCount, setPCGCount] = useState([]);
  const [cegCount, setCEGCount] = useState([]);
  const [materialCount, setMaterialCount] = useState([]);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({
    data: {},
    isVisible: false,
  });
  const { getDtCall, dtData:dtColumnsResponse } = useGenericDtCall();
  const { getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall();

  const rbSearchForm = useSelector((state) => state.commonFilter["RequestBench"]);
  const dashboardSearchForm = useSelector((state) => state?.commonFilter["Dashboard"]);
    const [openScheduler, setOpenScheduler] = useState(false);
  let iwaAccessData = useSelector(
    (state) => state?.userManagement?.entitiesAndActivities?.["Request Bench"]
  );
  const [openButton, setOpenButton] = useState(false);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [schedulerType, setSchedulerType] = useState("");
  const [filterDataScheduler, setFilterDataScheduler] = useState({
    module: 'All',
    requestType: 'All',
    status: 'Open'
  });
  const ShedularDependendDrodpDownData = {
    "Attachment Scheduler": {
      Open: [
        "Scheduler - Failed",
        "Scheduler - Pending",
        "Scheduler - Partially Completed",
        "Scheduler - Paused",
      ],
      Closed: ["Scheduler - Completed", "Scheduler - Canceled"],
    },
    "SAP Scheduler": {
      "tab0": {
        Open: [
          "Scheduler - Failed",
          "Scheduler - Pending",
          "Scheduler - Partially Completed",
          "Scheduler - Paused",
        ],
        Closed: ["Scheduler - Completed", "Scheduler - Canceled"],
      },
      "tab1": {
        Open: [
          "Adhoc Syndication - Partially Completed",
          "Adhoc Syndication - In Progress",
          "Adhoc Syndication - Failed",
        ],
        Closed: ["Adhoc Syndication - Completed", "Adhoc Syndication - Canceled"],
      },
    },
  };
  const [status, setStatus] = useState("Open"); // "Open" or "Closed"
  const [schedulerStatus, setSchedulerStatus] = useState([]);
  const [selectedDateRangeScheduler, setSelectedDateRangeScheduler] = useState([]);
  const [tabValue, setTabValue] = useState(0);
  const [priorityRow, setPriorityRow] = useState([]);
  const [iconClicked, setIconClicked] = useState({});
  const [sapAttachmentRow, setSAPAttachmentRow] = useState([]);
  const [errors, setErrors] = useState({
    module: false,
    requestType: false,
    status: false,
  });
  const [selectedTab, setSelectedTab] = useState("tab0"); // "tab0" or "tab1" (only for "Other Scheduler")
  const rbSearchFormScheduler = useSelector(
    (state) => state.commonFilter["SchedulerManager"]
  );
  const [successMsg, setsuccessMsg] = useState(false);
  const [generatedDynamicolumn,setGenerateDynamiccolumn] = useState([])
  const [searchParammeters,setSearchParameters] = useState([])
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const allOptions = [
    "Create",
    "Change",
    "Extend",
    "Create with Upload",
    "Change with Upload",
    "Extend with Upload",
    "Finance Costing",
  ];

  const names = [
    "Draft",
    "Data Entry Pending",
    "Sent Back By Intermediate User",
    "Submitted For Review",
    "Validated-MDM",
    "Validated-Requestor",
    "Validation Failed-MDM",
    "Validation Failed-Requestor",
    "Rejected",
    "Sent Back By MDM Team",
    "Syndicated In SAP",
    "Syndicated In SAP(Direct)",
    "Canceled",
    "Syndication Failed",
    "Syndication Failed(Direct)",
    "Syndicated-Partially",
    "Syndicated-Partially(Direct)",
    "Upload Failed",
    "Upload Successful",
  ];
  const parentStatusOptions = ["Draft", "In Progress", "Validated-Requestor", "Validation Failed-Requestor", "Upload Successful", "Upload Failed", "Completed", "Canceled"];

  const tempNames = [
    "Logistic Data",
    "MRP Data",
    "Warehouse View 2",
    "Item Cat Group",
    "Set to DNU",
    "Update Descriptions",
    "Change Status",
  ];

  const reqStatusString = rbSearchForm?.reqStatus.join("$^$");
  const reqTempNameString = rbSearchForm?.tempName.join("$^$");
  const getRequestStatus = () => {
    const hSuccess = (data) => {
      setReqStatus(data.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getRequestStatus`,
      "get",
      hSuccess,
      hError
    );
  };
  let tabsArray = [
    t("Material"),
    "Profit Center",
    "Cost Center",
    "Bank Key",
    "General Ledger",
    "Cost Center Group",
    "Profit Center Group",
    "Cost Element Group",
  ];
  let tabsArrayHashMap = {
    0: "Material",
    // "1":"Cost Center",
    // "2":"Profit Center",
    // "3":"Bank Key",
    // "4":"General Ledger"
  };

const createMultiValueCell = (fieldName, displayName) => ({
  field: fieldName,
  headerName: t(displayName),
  editable: false,
  flex: 1,
  renderCell: (params) => {
        if (params.value === "Not Available") return "Not Available";
        const materials = fieldName === "childRequestIds" ? params.value ? params.value.split(",").map(m => {
          return  m.trim();
        }) : [] :  params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = materials.length - 1;

    if (materials.length === 0) return "-";
        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0 
          }}>
            <Tooltip 
              title={materials[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {materials[0]}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        {t("Additional Materials")} ({displayCount})
                      </Typography>
                      {materials.slice(1).map((material, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {material}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "#3b30c8",
                        "&:hover": { color: "#2e25a0" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "#3b30c8",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },
})

const createDateValueCell = (fieldName, displayName) => ({
  field: fieldName,
  headerName: t(displayName),
  editable: false,
  flex: 1,
  renderCell: (params) => {
        return <Typography sx={{ fontSize: "12px" }}>{moment(params.row.createdOn).format(appSettings?.dateFormat)}</Typography>;
      },
})

const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1.4,
  })

const createStatusValueCell = (fieldName, displayName) => ({
  field: fieldName,
  headerName: t(displayName),
  editable: false,
  flex: 1.4,
  renderCell: (cellValues) => {
        return (
          <Chip
          sx={{
            justifyContent: "flex-start",
            borderRadius: "4px",
            color: "#000",
            width: "100%",
            minWidth: "4.6rem",
            fontSize: "12px",
            background: 
              colors.statusColorMap[
                cellValues.row.reqStatus.toLowerCase().replace(/[^a-z0-9]/gi, '')
              ] || colors.statusColorMap.default
          }}
          label={cellValues.row.reqStatus}
        />      
        );
      },
})

const createValueForRequestType = (fieldName, displayName) => ({
   field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
  renderCell: (params) => {
        const tempName = params.row.tempName;
        return (
          <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <span style={{ flex: 1, wordBreak: "break-word", whiteSpace: "normal" }}>{params.row.requestType}</span>
            {tempName !== "" && (
              <Tooltip arrow placement="right" title={<div style={{ maxHeight: "200px", overflowY: "auto" }}>{tempName}</div>}>
                <InfoIcon sx={{ 
                        fontSize: "1rem",
                        color: "primary.main",
                        "&:hover": { color: "primary.dark" }
                      }}  />
              </Tooltip>
            )}
          </div>
        );
      },
})

const createRequestBenchColums = (data) => {
  const columns = [];
  let sortedData = data?.sort(
  (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
  )|| [];
  if (sortedData) {
    sortedData?.forEach((item) => {
      if(item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY){
      if (item?.MDG_MAT_UI_FIELD_NAME) {
        const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
        const headerName = item.MDG_MAT_UI_FIELD_NAME;
        if (item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.MULTIPLE) {
          if(fieldName === "requestType"){
              columns.push(createValueForRequestType(fieldName, headerName))
          }else{
            columns.push(createMultiValueCell(fieldName, headerName));
          }
        }
        else if (item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.DATE) {
          columns.push(createDateValueCell(fieldName, headerName));
        }
        else if(item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.SINGLE){
          columns.push(createSingleValueCell(fieldName,headerName))
        }
        else if(item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.STATUS){
          columns.push(createStatusValueCell(fieldName,headerName))
        }
      }
    }
  });
  }
  columns.push({
    field: "actions",
    align: "center",
    flex: 1, // Use flex for responsive width
    headerAlign: "center",
    headerName: t("Actions"),
    sortable: false,
    renderCell: (params) => {
      
      if (isChildView) {
        return (
          <div>
            <Tooltip title="View Flow">
              <IconButton
                disabled={!shouldShowChildRequestHistoryButton(params?.row?.reqStatus)}
                aria-label="View Metadata"
                onClick={() => {
                  const prefix = params.row.requestId.substring(0, 3);
                  setLocalStorage(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK, {...params?.row,prefix});
                  dispatch(
                    commonSearchBarUpdate({
                      module: "RequestHistory",
                      filterData: {
                        reqId:  prefix + params.row.childRequestIds,
                      },
                    })
                  );
                  navigate(APP_END_POINTS.REQUEST_HISTORY,{state: {
                            requestId: params.row?.requestId,
                            module: params.row?.module, 
                         },});
                }}
              >
                <Workflow
                  color={
                    !shouldShowChildRequestHistoryButton(params?.row?.reqStatus)
                      ? "#808080"
                      : "#3B30C8"
                  }
                />
              </IconButton>
            </Tooltip>
  
            <Tooltip title={t("Cancel")}>
              <IconButton
                disabled={!shouldShowChildCancelButton(params?.row?.reqStatus)}
                aria-label="View Metadata"
                onClick={() => {
                  setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                }}
              >
                <DeleteForeverOutlined
                  sx={{
                    color: (theme) => (!shouldShowChildCancelButton(params?.row?.reqStatus) ? "#808080" : "#cc3300"),
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title={t("Error Report")}>
              <IconButton
                disabled={!shouldShowChildErrorReportButton(params?.row?.reqStatus)}
                onClick={() => {
                  navigate(`/requestBench/errorHistory?RequestId=${params?.row?.childRequestIds}`,{ state: { childRequest: true, module: params?.row?.module,display: true  } });
                }}
              >
                <SummarizeOutlinedIcon
                  sx={{
                    color: !shouldShowChildErrorReportButton(params?.row?.reqStatus) ? "#808080" : "#ffd93f",
                  }}
                />
              </IconButton>
            </Tooltip>
          </div>
        );
      } else {
        return (
          <div>
            <Tooltip title={t("Cancel")}>
              <IconButton
                disabled={!shouldShowParentCancelButtonParent(params?.row?.reqStatus)}
                aria-label="View Metadata"
                onClick={() => {
                  setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                }}
              >
                <DeleteForeverOutlined
                  sx={{
                    color: (theme) => (!shouldShowParentCancelButtonParent(params?.row?.reqStatus) ? "#808080" : "#cc3300"),
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title={t("Error Report")}> 
              <IconButton
                disabled={!shouldShowParentErrorReportButtonParent(params?.row?.reqStatus)}
                onClick={() => {
                   navigate(`/requestBench/errorHistory?RequestId=${params?.row?.requestId}`,{ state: { childRequest: false, module: params?.row?.module, display: true } });
                }}
              >
                <SummarizeOutlinedIcon
                  sx={{
                    color: !shouldShowParentErrorReportButtonParent(params?.row?.reqStatus) ? "#808080" : "#ffd93f",
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title={t("View Child Requests")}> 
              <IconButton 
              //disabled={!params.row.isBifurcated} 
                onClick={() => {
                  setSelectedRow(params.row);              
                  fetchBifurcationDetails(params.row?.requestId, params?.row?.module);      
                  setOpenPopup(true);
                }}
                disabled={shouldShowViewChildButton(params?.row?.reqStatus)}
              >
                <PreviewIcon sx={{ fontSize: "20px", color: shouldShowViewChildButton(params?.row?.reqStatus) ? "#808080" :`${colors.blue.indigo}` }}
                />
              </IconButton>
            </Tooltip>
          </div>
        );
      }
    },
  },)
  return columns;
}

const fetchColumnsFromDt = () => {
  let payload = {
        decisionTableId: null,
        decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
        version: "v1",
        conditions: [
          {
            "MDG_CONDITIONS.MDG_MAT_REGION":"US",
            "MDG_CONDITIONS.MDG_MODULE":"Material",
            "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Request Bench"
          },
        ],
      };
      getDtCall(payload);
}

const fetchSearchParameterFromDt = () => {
  let payload = {
        decisionTableId: null,
        decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
        version: "v1",
        conditions: [
          {
            "MDG_CONDITIONS.MDG_MAT_REGION":"US",
            "MDG_CONDITIONS.MDG_MODULE":"Material",
            "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Request Bench"
          },
        ],
      };
      getSearchParams(payload);
}


  useEffect(() => {
    fetchColumnsFromDt()
    fetchSearchParameterFromDt()
  },[])

  useEffect(() => {
    if (dtColumnsResponse) {
      const columnsGlobal = createRequestBenchColums(dtColumnsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
      setGenerateDynamiccolumn(columnsGlobal);
    }
    if(dtSearchParamsResponse){
      setSearchParameters(dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE);
    }
  }, [dtColumnsResponse,dtSearchParamsResponse,isChildView]);

  useEffect(() => {
    if (clearClicked) {
      getFilter(activeTab);
      setClearClicked(false);
    }
  }, [clearClicked]);
  useEffect (()=>{
    if (rbSearchFormScheduler?.createdOnScheduler) {
      const presentDateScheduler = new Date(rbSearchFormScheduler?.createdOnScheduler[0]);
      const backDateScheduler = new Date(rbSearchFormScheduler?.createdOnScheduler[1]);
      setSelectedDateRangeScheduler([presentDateScheduler,backDateScheduler]);
    }
  },[rbSearchFormScheduler])

  useEffect(() => {
    getRequestStatus();
    dispatch(setTaskData({}));
    dispatch(setIwmMyTask({}));
    dispatch(setIsSubmitDisabled(true));
    dispatch(clearPaginationData());
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
  }, []);
  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  const handleInputChange = (e) => {
  if (e.target.value !== null) {
    const fieldName = e.target.name;
    const tempRequestId = e.target.value;

    let tempFilterData = {
      ...rbSearchForm,
      [fieldName]: tempRequestId,
    };
    
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }
};


  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleStatus = (e) => {
    if (e.target.value !== null) {
      let tempStatus = e.target.value;

      if (tempStatus.includes("Select All")) {
        if (tempStatus.length === names.length + 1) {
          tempStatus = [];
        } else {
          tempStatus = [...names];
        }
      }

      let tempFilterData = {
        ...rbSearchForm,
        reqStatus: tempStatus,
      };

      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handletempName = (e) => {
    if (e.target.value !== null) {
      let tempName = e.target.value;

      if (tempName.includes("Select All")) {
        if (tempName.length === tempNames.length + 1) {
          tempName = [];
        } else {
          tempName = [...tempNames];
        }
      }

      let tempFilterData = {
        ...rbSearchForm,
        tempName: tempName,
      };

      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleCancel = (requestId, requestType, module) => {
    setLoaderMessage("Request processing please wait.");
    setBlurLoading(true);
    const { destination } = filterNavigation(module);
    let cancelPayload = {
      requestId: isChildView ? isDeleteDialogVisible?.data.row.requestId.slice(0,3) + requestId : requestId,
      requestType: requestType,
    };

    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`Request Cancelled `);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Failed Cancelling Requests");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        //setTestrunStatus(true);
      }
      handleClose();
      getFilter(activeTab);
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
    };
    const hError = () => {
      setLoaderMessage("");
      setBlurLoading(false);
      setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
    };

    doAjax(
      `/${destination}/workflow/cancelWorkflow`,
      "post",
      hSuccess,
      hError,
      cancelPayload
    );
  };

  const handleMassCancel = () => {
    if (selectedRows.length > 10) {
      setOpenSnackbar(true);
      setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES.MAX_CANCEL_LIMIT));
      setAlertType("warning");
      return;
    }
    setBlurLoading(true);
    const cancelPayload = selectedRows.map((id) => {
      const selectedRow = rmDataRows.find((row) => row.id === id);
      return {
        requestId: selectedRow.requestId,
        requestType: selectedRow.requestType,
      };
    });

    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === API_CODE.STATUS_200) {
        setOpenSnackbar(true);
        setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES.CANCEL_SUCCESS));
        setAlertType("success");
        setSelectedRows([]);
      } else {
        setOpenSnackbar(true);
        setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES.CANCEL_FAILED));
        setAlertType("error");
      }
      getFilter(activeTab);
    };

    const hError = () => {
      setLoaderMessage("");
      setBlurLoading(false);
    };

    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.WORK_FLOW.CANCEL_WORKFLOWS}`,
      "post",
      hSuccess,
      hError,
      cancelPayload
    );
  };

  const handleChangePriority = (event) => {
    const value = event.target.value;
    setSelectedOptions(typeof value === "string" ? value.split(",") : value);
    if (value !== null) {
      var tempPriority = value;

      let tempFilterData = {
        ...rbSearchForm,
        reqPriority: tempPriority,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleRequestType = (e) => {
    if (e.target.value !== null) {
      var tempRequestType = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        requestType: tempRequestType,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e) => {
    if (true) {
      var tempCreatedBy = e.target.value;

      let tempFilterData = {
        ...rbSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: tempFilterData,
        })
      );
    }
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);
  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e;
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: {
            ...rbSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleSelectAllDivision = () => {
    if (selectedDivision.length === dropDownData?.reqBenchDivision?.length) {
      setSelectedDivision([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedDivision(dropDownData?.reqBenchDivision);
    }
  };

  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === createdByOptions.length) {
      setSelectedCreatedBy([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedCreatedBy(createdByOptions);
    }
  };

  const handleSelectAllMateriaL = () => {
    if (selectedMaterial.length === materialOptions.length) {
      setSelectedMaterial([]);
      //TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedMaterial(materialOptions);
    }
  };

  const handleSelectAllReqType = () => {
    if (selectedReqType.length === allOptions.length) {
      setSelectedReqType([]);
      // TO BE USED LATER
      //setselectedPresetDescription([]);
    } else {
      setSelectedReqType(allOptions);
    }
  };
  const isMaterialSelected = (option) => {
    return selectedMaterial.some(
      (selectedOption) => selectedOption?.desc === option?.desc
    );
  };

  const isReqTypeSelected = (option) => {
    return selectedReqType.some((selectedOption) => selectedOption === option);
  };
  const isDivisionSelected = (option) => {
    return selectedDivision.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };
  // let [rbSearchForm, setrbSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows

  // const clearSearchBar = () => {
  //   setMaterialNumber("");
  // };
  const handleSearchAction = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      setCount(materialCount);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
            row?.[keys?.[k]]
              .toString()
              .toLowerCase()
              ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setCount(selected?.length);
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
  const [count, setCount] = useState(0);
  const [pcCount, setPcCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  useEffect(() => {
    if (page !== 0) {
      const requiredDataCount = pageSize * (page + 1);
      if (
        requiredDataCount > rmDataRows.length &&
        rmDataRows.length % pageSize === 0
      ) {
        getFilterBasedOnPagination(activeTab);
      }
    }
  }, [page]);



  getFilter = (activeTab = 0,fetchSkip=0, autoRefresh=false, selectAll=false) => {
    setIsLoading(true);
    setPage(0);

    var payload = {
      fromDate: moment(rbSearchForm?.createdOn[0] ? rbSearchForm?.createdOn[0] : dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate: moment(rbSearchForm?.createdOn[1] ? rbSearchForm?.createdOn[1] : dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      requestId: rbSearchForm?.requestId,
      childRequestId: rbSearchForm?.childRequestId,
      requestPriority: rbSearchForm?.reqPriority ? rbSearchForm?.reqPriority.join(",") : "",
      reqStatus: reqStatusString,
      reqType: rbSearchForm?.requestType,
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? rbSearchForm?.createdBy : userData?.emailId,
      division: rbSearchForm?.division,
      material: rbSearchForm?.number,
      top: selectAll ? (count || 10000) : pageSize, 
      skip: fetchSkip ?? 0,
      userId: userData?.user_id,
      templateName: reqTempNameString,
      fetchCount: false,
      isSunocoRB: false,
      isChild: isChildView,
      
    }
  

    let headers = autoRefresh
      ? {
          "X-RateLimit-Enabled": "true",
        }
      : {};

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/searchForRequestBench`;
    } else if (activeTab === 2) {
       url = `/${destination_CostCenter_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 1) {
       url = `/${destination_ProfitCenter_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 3) {
       url = `/${destination_BankKey}/data/searchForRequestBench`;
    } else if (activeTab === 4) {
       url = `/${destination_GeneralLedger_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 5) {
       url = `/${destination_CostCenter_Mass}/data/searchForRequestBenchCCG`;
    } else if (activeTab === 6) {
       url = `/${destination_ProfitCenter_Mass}/data/searchForRequestBenchPCG`;
    } else if (activeTab === 7) {
       url = `/${destination_GeneralLedger_Mass}/data/searchForRequestBenchCEG`;
    } else {
      return false;
    }
    const hSuccess = (data) => {
      setIsLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200) {
        dispatch(commonSearchBarClear({ module: "RequestBench" }));
        setMaterialCount(data?.body?.count);
        var rows = [];
        for (let index = 0; index < data?.body?.list.length; index++) {
          var tempObj = data?.body?.list[index];
            var tempRow = {
              id: tempObj?.requestId || tempObj?.id,
              requestId: tempObj?.requestId,
              requestType: tempObj?.requestType,
              createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
              changedOn: moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
              createdByUser: tempObj?.createdBy,
              reqStatus: tempObj?.requestStatus ? tempObj?.requestStatus : "-",
              salesOrg: tempObj?.salesOrg ? tempObj?.salesOrg : "-",
              distChnl: tempObj?.distChnl ? tempObj?.distChnl : "Not Available",
              plant: tempObj?.plantOrg,
              tempName: tempObj?.templateName,
              requestPriority: tempObj?.requestPriority ? tempObj?.requestPriority : "Not Available",
              materialNos: tempObj?.["objectNumbers"]?.length > 0 ? `${tempObj?.["objectNumbers"]}` : "Not Available",
              objectNumbers: tempObj?.["objectNumbers"]?.length > 0 ? `${tempObj?.["objectNumbers"]}` : "Not Available",
              changedOnAct: tempObj.lastChangeDate,
              parentNode: tempObj?.parentNode || [],
              isBifurcated: tempObj?.isBifurcated,
              childRequestIds: tempObj?.["childRequestIds"]?.length > 0 ? `${tempObj?.["childRequestIds"]}` : "Not Available",
              module : data?.body?.module || ""

               
            };
            rows.push(tempRow);
          
        }
        setRmDataRows(rows);
        setIsLoading(false);
        if(selectAll){
          setPage(Math.floor(rows?.length / pageSize));
        }
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data?.statusCode === API_CODE?.STATUS_429) {
        setMessageDialogMessage(data?.message);
        setAlertType("error");
        handleSnackBarOpen();
      }
    };
    const hError = (error) => {
      setIsLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, payload, headers);
  };

  
  const getFilterBasedOnPagination = (activeTab) => {
    setIsLoading(true);
    var payload = {
      fromDate: moment(rbSearchForm?.createdOn[0] ? rbSearchForm?.createdOn[0] : dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate: moment(rbSearchForm?.createdOn[1] ? rbSearchForm?.createdOn[1] : dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      requestId: rbSearchForm?.requestId,
      childRequestId: rbSearchForm?.childRequestId,
      requestPriority: rbSearchForm?.reqPriority ? rbSearchForm?.reqPriority.join(",") : "",
      reqStatus: reqStatusString,
      reqType: rbSearchForm?.requestType,
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? rbSearchForm?.createdBy : userData?.emailId,
      division: rbSearchForm?.division,
      material: rbSearchForm?.number,
      top: pageSize, 
      skip: pageSize * page ?? 0,
      userId: userData?.user_id,
      templateName: reqTempNameString,
      fetchCount: false,
      isSunocoRB: false,
      isChild: isChildView,
      
    }

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/searchForRequestBench`;
    } else if (activeTab === 2) {
       url = `/${destination_CostCenter_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 1) {
       url = `/${destination_ProfitCenter_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 3) {
       url = `/${destination_BankKey}/data/searchForRequestBench`;
    } else if (activeTab === 4) {
       url = `/${destination_GeneralLedger_Mass}/data/searchForRequestBench`;
    } else if (activeTab === 5) {
       url = `/${destination_CostCenter_Mass}/data/searchForRequestBenchCCG`;
    } else if (activeTab === 6) {
       url = `/${destination_ProfitCenter_Mass}/data/searchForRequestBenchPCG`;
    } else if (activeTab === 7) {
       url = `/${destination_GeneralLedger_Mass}/data/searchForRequestBenchCEG`;
    } else {
      return false;
    }
    const hSuccess = (data) => {
      setIsLoading(false);
      var rows = [];
      for (let index = 0; index < data?.body?.list.length; index++) {
        var tempObj = data?.body?.list[index];
        if (true) {
        var tempRow = {
              id: tempObj?.requestId || tempObj?.id,
              requestId: tempObj?.requestId,
              requestType: tempObj?.requestType,
              createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
              changedOn: moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
              createdByUser: tempObj?.createdBy,
              reqStatus: tempObj?.requestStatus ? tempObj?.requestStatus : "-",
              salesOrg: tempObj?.salesOrg ? tempObj?.salesOrg : "-",
              distChnl: tempObj?.distChnl ? tempObj?.distChnl : "Not Available",
              plant: tempObj?.plantOrg,
              tempName: tempObj?.templateName,
              requestPriority: tempObj?.requestPriority ? tempObj?.requestPriority : "Not Available",
              materialNos: tempObj?.["objectNumbers"]?.length > 0 ? `${tempObj?.["objectNumbers"]}` : "Not Available",
              objectNumbers: tempObj?.["objectNumbers"]?.length > 0 ? `${tempObj?.["objectNumbers"]}` : "Not Available",
              changedOnAct: tempObj.lastChangeDate,
              parentNode: tempObj?.parentNode || [],
              isBifurcated: tempObj?.isBifurcated,
              childRequestIds: tempObj?.["childRequestIds"]?.length > 0 ? `${tempObj?.["childRequestIds"]}` : "Not Available",
              module : data?.body?.module || ""

               
            };
          rows.push(tempRow);
        }
      }
      
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setIsLoading(false);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      setIsLoading(false);
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };


  const handleTable = () => {
    setIsLoading(true);
    let payload = {
      top: 1000,
      skip: 0,
      userId: userData?.user_id,
    };

    if (activeTab === 0) {
      var url = `/${destination_MaterialMgmt}/data/getItemsForRequestBench`;
    } else if (activeTab === 1) {
      var url = `/${destination_CostCenter_Mass}/data/getItemsForRequestBench`;
    } else if (activeTab === 2) {
      var url = `/${destination_ProfitCenter_Mass}/data/getItemsForRequestBench`;
    } else if (activeTab === 3) {
      var url = `/${destination_BankKey}/data/getItemsForRequestBench`;
    } else if (activeTab === 4) {
      var url = `/${destination_GeneralLedger_Mass}/data/getItemsForRequestBench`;
    } else {
      return false;
    }

    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list.length; index++) {
        var tempObj = data?.body?.list[index];
        
        if (true) {
          var tempRow = {
            id: tempObj?.id,
            requestId: tempObj?.requestId,
            requestType: tempObj?.requestType,
            createdOn: moment(tempObj.creationDate).format("DD MMM YYYY") ?? "",
            changedOn:
              moment(tempObj.lastChangeDate).format("DD MMM YYYY") ?? "",
            createdBy: tempObj?.createdByUser,
            reqStatus: tempObj?.reqStatus,
            salesOrg: tempObj?.salesOrg,
            requestPriority: tempObj?.requestPriority,
            distChnl: tempObj?.distChnl,
            plantOrg: tempObj?.plantOrg,
            ControllingArea: tempObj?.ControllingArea,
            CostCenter: tempObj?.CostCenter,
            profitCenter: tempObj?.profitCenter,
            profitCenterName: tempObj?.profitCenterName,
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows(rows);
      setIsLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleAllDataDialogOpen = () => {
    setOpenAllDataDialog(true);
  };
  const handleAllDataDialogClose = () => {
    setOpenAllDataDialog(false);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setOpendialog3(false);
    setOpendialog(false);
    setOpendialog2(false);
  };

  const openAnchor = Boolean(anchorEl_Preset);

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    setSelectedCreatedBy([]);
    setSelectedMaterial([]);
    setSelectedDivision([]);
    setSelectedReqType([]);
    setSelectedOptions([]);
    let tempFilterData = {
      ...rbSearchForm,
      reqPriority: "",
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );

    dispatch(commonFilterClear({ module: "RequestBench", days: 7 }));
    setClearClicked(true);
  };
  useEffect(() => {
    setinitialLoad(false);
    
    return () => {
      dispatch(
        initialDataUpdate({
          module: "RequestBench",
          initialData: [],
        })
      );
      dispatch(commonFilterClear({ module: "RequestBench", days: 7 }));
    };
  }, []);
  const onRowsSelectionHandler = (ids) => {
    setSelectedRows(ids);
    const selectedRowsData = ids.map((id) => rmDataRows.find((row) => row.id === id));
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };
  function refreshPage() {
    getFilter(activeTab);
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);

  // const handleMatCodeClick = (event) => {
  //   if (materialDetails !== null) setMaterialDetails(null);
  //   else fetchMaterialDetails(event.target.innerText);
  //   setMatAnchorEl(matAnchorEl ? null : event.currentTarget);
  // };

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  function generateHtmlForPdf(
    rowsDataForPayload,
    firstDataOfDisplay,
    secondDataOfDisplay,
    comments,
    changelogData,
    workFlowData
  ) {
    return (
      <>
        <div>
          <div
            style={{
              background: "#EAE9FF",
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <div style={{ marginTop: 2, marginLeft: 2 }}>
              <h6>Request ID: {rowsDataForPayload?.requestId}</h6>
            </div>
            <div style={{ outerContainer_Information }}>
              <h2>
                <strong>SUMMARY</strong>
              </h2>
            </div>

            <div alignItems={"end"}>
              <Typography>
                Created By: {rowsDataForPayload?.createdBy}
              </Typography>
              <Typography>
                Created On: {rowsDataForPayload?.createdOn}
              </Typography>
            </div>
          </div>

          <div style={{ marginLeft: "5%", display: "flex" }}>
            <div style={{ width: "50%" }}>
              <div style={{ display: "flex" }}>
                <div style={{ width: "50%" }}>
                  <h5>
                    <u>Field</u>
                  </h5>
                </div>

                <div>
                  <h5>
                    <u>Value</u>
                  </h5>
                </div>
              </div>
              {firstDataOfDisplay.map((item) => {
                return (
                  <div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "50%" }}>
                        {console.log(item)}
                        <h6>{item[0]}</h6>
                      </div>

                      <div>
                        <p>
                          {item[1] != ""
                            ? item[1]
                            : item[1] === true
                            ? "Yes"
                            : item[1] === false
                            ? "No"
                            : "-"}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div style={{ width: "50%" }}>
              <div style={{ display: "flex" }}>
                <div style={{ width: "50%" }}>
                  <h5>
                    <u>Field</u>
                  </h5>
                </div>

                <div>
                  <h5>
                    <u>Value</u>
                  </h5>
                </div>
              </div>
              {secondDataOfDisplay.map((item) => {
                return (
                  <div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "50%" }}>
                        {console.log(item)}
                        <h6>{item[0]}</h6>
                      </div>

                      <div>
                        <p>
                          {item[1] != ""
                            ? item[1]
                            : item[1] === true
                            ? "Yes"
                            : item[1] === false
                            ? "No"
                            : "-"}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          <div style={{ marginLeft: "5%", marginTop: "5%" }}>
            <h4>
              <u>Comments</u>
            </h4>
            <div>
              <h6>
                {comments.map((item) => {
                  return (
                    <ul>
                      <li> {`${item?.comment}    (${item?.user}) `}</li>
                    </ul>
                  );
                })}
              </h6>
            </div>
          </div>
          <div style={{ margin: "5%" }}>
            <h4>
              <u>Change Log</u>
            </h4>
            <div>
              <h6>
                {/* {comments.map((item) => {
                      return ( */}
                <table style={{ width: "100%", border: "1px solid black" }}>
                  <tr>
                    <th style={{ border: "1px solid black" }}>Field Name</th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Old Value
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      New Value
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Updated By
                    </th>
                    <th
                      style={{
                        border: "1px solid black",
                        textAlign: "left",
                        padding: "8px",
                      }}
                    >
                      Updated On
                    </th>
                  </tr>

                  {changelogData.map((item) => {
                    return (
                      <tr>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.FieldName}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.PreviousValue}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.CurrentValue}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {item?.ChangedBy}
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "8px",
                          }}
                        >
                          {moment(item?.ChangedOn).format(
                            appSettings?.dateFormat
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </table>
                {/* );
                    })} */}
              </h6>
            </div>
          </div>
          <div style={{ margin: "5%" }}>
            <h4>
              <u>Task Flow</u>
            </h4>

            <div>
              <div
                style={{
                  border: "1px solid",
                  borderRadius: "10px",
                  padding: "10px",
                  width: "50%",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <h6>Soumarya Sarkar</h6>
                  <p>24/02/2024</p>
                </div>
                <hr style={{ margin: "0px" }}></hr>
                <div style={{ marginTop: "5px" }}>
                  <h5>Process Started</h5>
                </div>
                <div style={{ marginTop: "4%" }}>
                  <i>Task Completion Time : 30 minutes </i>
                </div>
              </div>
              {workFlowData.map((item) => {
                return (
                  <>
                    <div style={{ width: "50%" }}>
                      <b
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          paddingBottom: "1%",
                        }}
                      >
                        {" "}
                        <p>|</p>
                      </b>
                    </div>
                    <div
                      style={{
                        border: "1px solid",
                        borderRadius: "10px",
                        padding: "10px",
                        width: "50%",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <h6>{item?.processor}</h6>
                        <p>{item?.createdAt}</p>
                      </div>
                      <hr style={{ margin: "0px" }}></hr>
                      <div style={{ marginTop: "5px" }}>
                        <h5>{item?.subject}</h5>
                      </div>
                      <div style={{ marginTop: "4%" }}>
                        <i>Task Completion Time : {item?.completedAt} </i>
                      </div>
                    </div>
                  </>
                );
              })}
            </div>
          </div>
        </div>
      </>
    );
  }

  const handleDownloadClick = async (rowData) => {
    const pdfBlob = await generatePDF(rowData);
    if (pdfBlob) {
      const pdfData = URL.createObjectURL(pdfBlob); // Convert the blob to a URL
      const downloadLink = document.createElement("a"); // Create a download link
      downloadLink.href = pdfData; // Set the URL as the download link's href
      downloadLink.download = "summary-report.pdf"; // Set the download filename
      document.body.appendChild(downloadLink); // Append the download link to the document body
      downloadLink.click(); // Trigger the download
      document.body.removeChild(downloadLink); // Remove the download link from the document body
    } else {
      console.log("Error: PDF blob is null or undefined");
    }
  };

  let generatePDF = async (rowData) => {
    let fetchData = (rowsDataForPayload) => {
      let payload = {};
      let url = "";
      if (
        rowsDataForPayload?.requestId?.includes("NCS") ||
        rowsDataForPayload?.requestId?.includes("CCS")
      ) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          costCenter: rowsDataForPayload?.costCenter ?? "",
          controllingArea: rowsDataForPayload?.controllingAreaDataCopy ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_CostCenter_Mass}/data/displayCostCenter`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NPS") ||
        rowsDataForPayload?.requestId?.includes("CPS")
      ) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          profitCenter: rowsDataForPayload?.profitCenter ?? "",
          controllingArea: rowsDataForPayload?.controllingArea ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_ProfitCenter_Mass}/data/displayProfitCenter`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NLS") ||
        rowsDataForPayload?.requestId?.includes("CLS")
      ) {
        console.log("inside if");
        payload = {
          id: rowsDataForPayload?.id ?? "",
          glAccount: rowsDataForPayload?.glAccount ?? "",
          compCode: rowsDataForPayload?.compCode ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_GeneralLedger_Mass}/data/displayGeneralLedger`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NBS") ||
        rowsDataForPayload?.requestId?.includes("CBS")
      ) {
        payload = {
          id: rowsDataForPayload?.id ?? "",
          bankCtry: rowsDataForPayload?.bankCtryReg ?? "",
          bankKey: rowsDataForPayload?.bankKey ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_BankKey}/data/displayBankKey`;
      } else if (rowsDataForPayload?.requestId?.includes("NMS")) {
        payload = {
          id: rowsDataForPayload?.id ?? "",
          materialNo: rowsDataForPayload?.materialNo ?? "",
          plantOrg: rowsDataForPayload?.plantOrg ?? "",
          salesOrg: rowsDataForPayload?.salesOrg ?? "",
          distChnl: rowsDataForPayload?.distChnl ?? "",
          reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
          screenName: rowsDataForPayload?.requestType ?? "Change",
        };
        url = `/${destination_MaterialMgmt}/data/displayMaterial`;
      }

      const hError = (error) => {
        console.log(error);
      };
      return promiseAjax(url, "post", payload);
      // console.log("payload", payload);
    };

    let fetchCommentData = (rowsDataForPayload) => {
      let urForComments = "";
      if (
        rowsDataForPayload?.requestId?.includes("NCS") ||
        rowsDataForPayload?.requestId?.includes("CCS")
      ) {
        console.log("inside if");
        urForComments = `/${destination_CostCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NPS") ||
        rowsDataForPayload?.requestId?.includes("CPS")
      ) {
        console.log("inside if");
        urForComments = `/${destination_ProfitCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NLS") ||
        rowsDataForPayload?.requestId?.includes("CLS")
      ) {
        console.log("inside if");
        urForComments = `/${destination_GeneralLedger_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NBS") ||
        rowsDataForPayload?.requestId?.includes("CBS")
      ) {
        urForComments = `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urForComments, "get");
    };
    let fetchChangeLogData = (rowsDataForPayload) => {
      let changeLogUrl = "";
      if (
        rowsDataForPayload?.requestId?.includes("NCS") ||
        rowsDataForPayload?.requestId?.includes("CCS")
      ) {
        console.log("inside if");
        changeLogUrl = `/${destination_CostCenter_Mass}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
          3
        )}&isMass=${false}&coaCostCenter=${
          rowsDataForPayload?.controllingArea + rowsDataForPayload?.costCenter
        }`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NPS") ||
        rowsDataForPayload?.requestId?.includes("CPS")
      ) {
        console.log("inside if");
        changeLogUrl = `/${destination_ProfitCenter_Mass}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
          3
        )}&isMass=${false}&coaProfitCenter=${
          rowsDataForPayload?.controllingArea + rowsDataForPayload?.profitCenter
        }`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NLS") ||
        rowsDataForPayload?.requestId?.includes("CLS")
      ) {
        console.log("inside if");
        changeLogUrl = `/${destination_GeneralLedger_Mass}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
          3
        )}&isMass=${false}&compCodeGlAcc=${
          rowsDataForPayload?.compCode + rowsDataForPayload?.glAccount
        }`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NBS") ||
        rowsDataForPayload?.requestId?.includes("CBS")
      ) {
        changeLogUrl = `/${destination_BankKey}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
          3
        )}&isMass=${false}&countryBankKey=${
          rowsDataForPayload?.bankCtryReg + rowsDataForPayload?.bankKey
        }`;
      }
      return promiseAjax(changeLogUrl, "get");
    };

    let fetchWorkFlowData = (rowsDataForPayload) => {
      let urlForWorkFlow = "";
      if (
        rowsDataForPayload?.requestId?.includes("NCS") ||
        rowsDataForPayload?.requestId?.includes("CCS")
      ) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_CostCenter_Mass}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NPS") ||
        rowsDataForPayload?.requestId?.includes("CPS") ||
        rowsDataForPayload?.requestId?.includes("PCTNEW")
      ) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_ProfitCenter_Mass}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NLS") ||
        rowsDataForPayload?.requestId?.includes("CLS")
      ) {
        console.log("inside if");
        urlForWorkFlow = `/${destination_GeneralLedger_Mass}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NBS") ||
        rowsDataForPayload?.requestId?.includes("CBS")
      ) {
        urlForWorkFlow = `/${destination_BankKey}/activitylog/fetchRequestHistory?requestId=${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urlForWorkFlow, "get");
    };

    let data = await (await fetchData(rowData)).json();
    let commentData = await (await fetchCommentData(rowData)).json();
    let changeLogData = await (await fetchChangeLogData(rowData)).json();
    let workFlowData = await (await fetchWorkFlowData(rowData)).json();
    // console.log("commentData", data, commentData);
    // console.log("fetchWorkFlowData", workFlowData);
    const responseBody = data.body.viewData;
    let viewDataArray = Object.entries(responseBody);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]);
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[field.fieldName] = field.value;
        });
      });
      return item;
    });
    setDisplayData(toSetArray);
    let displayData = {};
    let firstDisplayData = [];
    let secondDisplayData = [];
    displayData = Object.entries(toSetArray);
    
    if (displayData && displayData.length > 0) {
      let firstRange = displayData.length / 2;
      firstDisplayData = displayData.slice(0, firstRange);
      secondDisplayData = displayData.slice(-firstRange);
    }
    var commentRows = [];
    commentData.body.forEach((cmt) => {
      var tempRow = {
        id: cmt.requestId,
        comment: cmt.comment,
        user: cmt.createdByUser,
        createdAt: cmt.updatedAt,
      };
      commentRows.push(tempRow);
    });

    var changeLogRows = [];
    for (let index = 0; index < changeLogData?.body?.length; index++) {
      var tempObj = changeLogData?.body[index];
      var tempRow = {
        id: uuidv4(),
        ChangedBy: tempObj.ChangedBy,
        ChangedOn: tempObj.ChangedOn,
        FieldName: tempObj.FieldName,
        PreviousValue: tempObj.PreviousValue,
        CurrentValue: tempObj.CurrentValue,
      };
      changeLogRows.push(tempRow);
    }
    let html = generateHtmlForPdf(
      rowData,
      firstDisplayData,
      secondDisplayData,
      commentRows,
      changeLogRows,
      workFlowData?.body
    );
    const pdfBlob = await exportAsPDF(html);
    return pdfBlob;
  };

  // const [mergedPdfBlob, setMergedPdfBlob] = useState(null);

  const generateMergedPdf = async (rowData) => {
    const firstBlob = await generatePDF(rowData);
    console.log("firstBlob", firstBlob);

    let fetchAttachmentsData = (rowsDataForPayload) => {
      let urlForAttachments = "";
      if (
        rowsDataForPayload?.requestId?.includes("NCS") ||
        rowsDataForPayload?.requestId?.includes("CCS")
      ) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NPS") ||
        rowsDataForPayload?.requestId?.includes("CPS")
      ) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      } else if (
        rowsDataForPayload?.requestId?.includes("NLS") ||
        rowsDataForPayload?.requestId?.includes("CLS")
      ) {
        urlForAttachments = `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${rowsDataForPayload?.requestId}`;
      }
      return promiseAjax(urlForAttachments, "get");
    };
    let attachmentsFetchedData = await (
      await fetchAttachmentsData(rowData)
    ).json();
    console.log("attachmentsFetchedData", attachmentsFetchedData);
    const attachmentsData = attachmentsFetchedData.documentDetailDtoList;
    console.log("attachmentsData", attachmentsData);

    let attachmentRows = [];
    attachmentsFetchedData?.documentDetailDtoList?.forEach((doc) => {
      if (true) {
        attachmentRows.push({
          id: doc.documentId,
          docType: doc.fileType,
          name: doc.fileName,
          uploadedBy: doc.createdBy,
          metaData: doc.metaDataTag ?? "",
          viewUrl: doc.documentViewUrl,
        });
      }
    });
    // console.log("temprows", tempRows);
    // setattachmentData(tempRows);
    // debugger
    console.log("attachmentData", attachmentData);
    // debugger;
    // setTimeout(() => {
    handlePdfDownload(firstBlob, attachmentRows);
    // }, 3000);
    // setJsPdfBlob(firstBlob);
  };

  const handlePdfDownload = async (firstBlob, attachmentRows) => {
    const pdfDoc = await PDFDocument.create();

    if (firstBlob) {
      const arrayBuffer = await firstBlob.arrayBuffer();
      const externalPdfDoc = await PDFDocument.load(arrayBuffer);
      const copiedPages = await pdfDoc.copyPages(
        externalPdfDoc,
        externalPdfDoc.getPageIndices()
      );
      copiedPages.forEach((page) => pdfDoc.addPage(page));
    }

    // console.log("attachmentData", attachmentData);
    for (const attachment of attachmentRows) {
      console.log("attachment", attachment);
      if (attachment.docType === "application/pdf") {
        const pdfBlob = await fetch(attachment.viewUrl).then((res) =>
          res.blob()
        );
        console.log("pdfBlob", pdfBlob);

        // Check if pdfBlob is not null before accessing its arrayBuffer
        if (pdfBlob) {
          const arrayBuffer = await pdfBlob.arrayBuffer();
          const externalPdfDoc = await PDFDocument.load(arrayBuffer);
          const copiedPages = await pdfDoc.copyPages(
            externalPdfDoc,
            externalPdfDoc.getPageIndices()
          );
          copiedPages.forEach((page) => pdfDoc.addPage(page));
        }
      }
    }

    const pdfBytes = await pdfDoc.save();

    const blob = new Blob([pdfBytes], { type: "application/pdf" });
    const downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = "Merged-pdf.pdf";
    downloadLink.click();
  };


  const titleToFieldMapping = {
    "Request ID": "requestId",
    "Request Type": "requestType",
    "Created At": "createdOn",
    "Updated At": "changedOn",
    "Created By": "createdBy",
    Status: "reqStatus",
    // Add more mappings as needed
  };

  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null);
  const allColumns = [...generatedDynamicolumn, ...dynamicFilterColumns];
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      savePDF({
        fileName: `Request Bench Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          {t("Download")}
        </Button>
      );
    },
  };

  const capitalize = (str) => {
    //  str.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  useEffect(() => {
    getFilter(activeTab)
  }, [activeTab, pageSize,isChildView]);


  let ref_elementForExport = useRef(null);
  
  const getDivision = (inputValue) => {
    setDivisionDropDownLoading(true);
    let payload = {
      division: inputValue,
    };
    const hSuccess = (data) => {
      setDivisionDropDownLoading(false);
      dispatch(setDropDown({ keyName: "reqBenchDivision", data: data.body }));
    };
    const hError = (error) => {
      setDivisionDropDownLoading(false);
      customError(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.DIVISION}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCreatedBy = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      createdBy: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setCreatedByOptions(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      customError(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.CREATED_BY}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getMaterialSearch = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      material: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setMaterialOptions(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getSearchParamMaterial`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    setMatInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(inputValue);
      }, 500);

      setTimerId(newTimerId);
    }
  };

  const handleDivInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getDivision(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getCreatedBy(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const getLabOffice = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LabOffice", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGenItemCatGroup = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "GeneralItemCategorGroup", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getGenItemCatGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProductAllocation = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProductAllocation", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getProdAllocation`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProductHeirarchy = () => {
    //haven't used now
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProdHier", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getProdHier`,
      "get",
      hSuccess,
      hError
    );
  };
  const getWeightUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "WeightUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getWeightUnit`,
      "get",
      hSuccess,
      hError
    );
  };
  const getVolumeUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "VolumeUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getVolumeUnit`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMedium = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Medium", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMedium`,
      "get",
      hSuccess,
      hError
    );
  };
  const getDgIndicatorProfile = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "dgIndiProfile", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getDgIndCator`,
      "get",
      hSuccess,
      hError
    );
  };
  const getEANCategory = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CategoryOfInternationalArticleNumberEAN",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getEanCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getXPlantMaterialStatus = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CrossPlantMaterialStatus", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getXPlant`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSegmentationStructure = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "SegmentationStructure", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getSegStructure`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSegmentationStrategy = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "SegmentationStrategy", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getSegStrategy`,
      "get",
      hSuccess,
      hError
    );
  };
  const getANPCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ANPCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getANPCode`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMRPType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMRPType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMRPGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMRPGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSalesUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SalesUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getSalesUnit`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMRPController = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "MRPController", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMRPController`,
      "get",
      hSuccess,
      hError
    );
  };

  const getUnitOfMeasureGroup = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "UnitsOfMeasureGroup", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getUnitOfMeasureGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getPurchasingGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PurchasingGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getPurchaseGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTaxCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getTaxCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getPlantSplMatStatus = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PlantSpMatStatus:", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getPlantSpMatlStatus`,
      "get",
      hSuccess,
      hError
    );
  };
  const getABCIndicator = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ABCIndicator", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getABCIndicator`,
      "get",
      hSuccess,
      hError
    );
  };
  const getStdHUType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "StdHUType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getStandHUType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getQualityInspectionGroup = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "QualityInspectionGroup", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getQualInspGrp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getQuarantinePeriod = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "QuarantinePeriod", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getBaseUOM`,
      "get",
      hSuccess,
      hError
    );
  };
  const getDeliveryUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "DeliveryUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getBaseUOM`,
      "get",
      hSuccess,
      hError
    );
  };
  const getOrderUnit = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "OrderUnit", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getBaseUOM`,
      "get",
      hSuccess,
      hError
    );
  };
  const getItemCategoryGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ItemCat", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getGenItemCatGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const geGentItemCategoryGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ItemCat", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getGenItemCatGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProductShape = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProductShape", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getProductShape`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHandlingIndicator = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HandlingIndicator", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getLogHandlingInd`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBasicMaterial = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BasicMaterial", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getBasicMaterial`,
      "get",
      hSuccess,
      hError
    );
  };
  const getWarehouseMaterialGroup = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "WarehouseMaterialGroup", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getWHMaterialGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getWarehouseStorageConsition = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "WarehouseStorageCondition", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data//getWhseStorCondition`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCwProfileForCwQuantity = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CWProfileForCWQty", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getCWProfile`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCatchWeightToleranceGroup = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CatchWTToleranceGroup", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getCatchWtTolGrp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRefProductForPkgBuilding = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "RefProductForPackagingBuilding",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getReferenceProduct`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMaterialGroupForPckMaterial = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "MaterialGroupPackagingMaterials",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMatGrpPack`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProductOrientationProfile = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ProductOrientationProfile", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getProductOrientationProfile`,
      "get",
      hSuccess,
      hError
    );
  };
  const getClassType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ClassType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getClassBasedOnClassType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(basicDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSalesDetails = () => {
    let viewName = "Sales";
    const hSuccess = (data) => {
      dispatch(salesDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getPurchasingData = () => {
    let viewName = "Purchasing";
    const hSuccess = (data) => {
      dispatch(purchasingDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMRPData = () => {
    let viewName = "MRP";
    const hSuccess = (data) => {
      dispatch(mrpDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountingData = () => {
    let viewName = "Accounting";
    const hSuccess = (data) => {
      dispatch(accountingDataTabs(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
  };
  useEffect(() => {
    let tempRequestType;

    if (selectedReqType.length === 1) {
      // If single selection, send as string
      tempRequestType = selectedReqType[0];
    } else if (selectedReqType.length > 1) {
      // If multiple selections, join with $^$
      tempRequestType = selectedReqType.map((item) => item).join("$^$");
    } else {
      // If no selection, send empty string
      tempRequestType = "";
    }

    let tempFilterData = {
      ...rbSearchForm,
      requestType: tempRequestType,
    };

    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedReqType]);

  useEffect(() => {
    var tempMatNum = selectedMaterial.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      number: tempMatNum,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedMaterial]);

  useEffect(() => {
    var tempDivision = selectedDivision.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      division: tempDivision,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedDivision]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rbSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingAreaPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroupForSearch = (searchControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${searchControllingArea}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingArea = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCodeBasedOnControllingArea",
          data: data.body.map((x, idx) => {
            return {
              id: idx,
              companyCodes: x.code,
              companyName: "",
              assigned: "X",
            };
          }),
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${newControllingArea.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCode`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroup = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${newControllingArea.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSegment = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Segment", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setProfitCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getIndicatorsProfitCenter = () => {
    let viewName = "Indicators";
    const hSuccess = (data) => {
      console.log("profit", data);
      dispatch(setProfitCenterIndicatorsTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompCodesProfitCenter = () => {
    let viewName = "Comp Codes";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCompCodesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressProfitCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setProfitCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationProfitCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryProfitCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setProfitCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getUserResponsiblePC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFormPlanningTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FormPlanningTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdictionPC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxJur", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };


  const fetchBifurcationDetails = (requestId, module) => {
      setIsLoading(true);
      const { destination,childRequestUrl } = filterNavigation(module,requestId);
      const hSuccess = (response) => {
        setApiData(response.body);
        setIsLoading(false);
      };
   
      const hError = (err) => {
        setIsLoading(false);
      };
    if(childRequestUrl){
      doAjax(
        `/${destination}${childRequestUrl}`,
        "get",
        hSuccess,
        hError,
      );
    }
    };
  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenterCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  // const getHierarchyArea = () => {
  //   var HA = "TZUS";
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
  //     console.log("data",data);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getHierarchyArea?controllingArea=${HA}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  // const getCompanyCode = () => {
  //   var HA = "TZUS";
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCompCode?contrllingArea=${HA}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getActyIndepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ActyIndepFormPlngTemp", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyDepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ActyDepFormPlngTemp", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyIndepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyIndepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyDepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyDepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplActStatKeyFigure = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "TemplActStatKeyFigure", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegionCC = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKeyCC = (data) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setCostCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControlCostCenter = () => {
    let viewName = "Control";
    const hSuccess = (data) => {
      dispatch(setCostCenterControlTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplatesCostCenter = () => {
    let viewName = "Templates";
    const hSuccess = (data) => {
      dispatch(setCostCenterTemplatesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressCostCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setCostCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationCostCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setCostCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const updatedColumns = [
    {
      field: "__check__",
      width: 50,
      renderHeader: () => null,
      headerClassName: "hide-select-all",
    },
    ...generatedDynamicolumn,
  ];

  const isRowSelectable = (params) => {
    const status = params.row.reqStatus;
    return !(
      status === REQUEST_STATUS.REJECTED ||
      status === REQUEST_STATUS.SYNDICATED_IN_SAP ||
      status === REQUEST_STATUS.CANCELED ||
      status === REQUEST_STATUS.UPLOAD_FAILED ||
      status === REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT ||
      status === REQUEST_STATUS.SYNDICATED_PARTIALLY_DIRECT ||
      status === REQUEST_STATUS.SYNDICATED_PARTIALLY
    );
  };
  const handleSelectAllData = () => {
    getFilter(0,0, false, true); 
  };


  useEffect(() => {
    if (rmDataRows && rmDataRows.length > 0) {
      
      
      rmDataRows.forEach((row) => {
        const isBifurcated =
          row.isBifurcated === true || row.isBifurcated === "true";
        if (isBifurcated && !row.childRequestIds) {
          console.log(selectedRow, "SelectedRoData");
          fetchBifurcationDetails(row.id,); // pass only row.id
        }
      });
    }
  }, []);

  const tabContents = [
    [
      <ReusableTable
        isLoading={isLoading}
        paginationLoading={isLoading}
        module={"RequestBench"}
        width="100%"
        title={t("Request Details List")}
        rows={tableData ?? []}
        columns={updatedColumns}
        onSearch={(value) => handleSearchAction(value)}
        onRefresh={refreshPage}
        page={page}
        showSearch={true}
        showRefresh={true}
        showSelectedCount={true}
        showExport={true}
        pageSize={pageSize}
        rowCount={count ?? rmDataRows?.length ?? 0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        getRowIdValue={"id"}
        hideFooter={true}
        tempheight={"calc(100vh - 320px)"}
        checkboxSelection={false}
        disableSelectionOnClick={true}
        isRowSelectable={isRowSelectable}
        status_onRowDoubleClick={true}
        onRowsSelectionHandler={onRowsSelectionHandler}
        callback_onRowDoubleClick={(params) => {
          const isBifurcated = params.row.isBifurcated === true || params.row.isBifurcated === "true";          
          const RequestId = params.row.requestId;
          const requestType = params.row.requestType;
          const module = params.row.module;
          if (params?.row?.reqStatus === REQUEST_STATUS?.CANCELED) {
            setOpenSnackbar(true);
            setMessageDialogMessage(ERROR_MESSAGES?.CANCELED_ERR);
            setAlertType("error");
            return;
          }
          const { navigation } = filterNavigation(module);
          if(isBifurcated) {
            if(isChildView){
              navigate(`${navigation}?RequestId=${params?.row?.childRequestIds}&RequestType=${requestType}&reqBench=${true}`, {
                state: params.row,
              });
            }
            else{ 
              return 
            }
          }
          else{
          navigate(`${navigation}?RequestId=${RequestId}&RequestType=${requestType}&reqBench=${true}`, {
            state: params.row,
          });
          }
        }}
        stopPropagation_Column={"action"}
        showCustomNavigation={true}
        onSelectAllOptions={handleSelectAllData}
        showSelectAllOptions ={true}
        showFirstPageoptions ={true}
        onSelectFirstPageOptions={()=>{setPage(0)}}
      />,
    ]
  ];

  const handleDelete = () => {
    handleCancel(isChildView ? isDeleteDialogVisible?.data.row.childRequestIds : isDeleteDialogVisible?.data.row.requestId, isDeleteDialogVisible?.data.row.requestType, isDeleteDialogVisible?.data.row.module);
  };
  const SelectionSummaryfn = () => {
    return (
      <SelectionSummary
        selectedRows={selectedRows}
        count={count}
        tableData={tableData}
        handleMassCancel={handleMassCancel}
      />
    );
  };
  const priorityColumns = [
    {
      field: "id",
      headerName: "",
      editable: false,
      flex: 1,
      hide: true,
      // width: "10%",
    },
    {
      field: "reqId",
      headerName: t("Request ID"),
      editable: false,
      flex: 2,
      // width: "20%",
    },
    {
      field: "module",
      headerName: t("Module"),
      editable: false,
      flex: 2,
      // width: "20%",
    },
    {
      field: "reqType",
      headerName: t("Request Type"),
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "scheduledBy",
      headerName: t("Scheduled By"),
      editable: false,
      flex: 2.5,
      // width: "15%",
    },
    {
      field: "scheduledOn",
      headerName: t("Scheduled On"),
      editable: false,
      flex: 1.2,
      // width: "10%",
    },
    {
      field: "totalObjects",
      headerName: t("Total Objects"),
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count scheduled for SAP syndication" arrow placement="top">
          <span>{t("Request ID")}</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "pendingObjects",
      headerName: t("Pending Objects"),
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count pending for SAP syndicated." arrow placement="top">
          <span>{t("Request ID")}</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "objectSuccessCount",
      headerName: t("Success Objects"),
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count syndicated in SAP" arrow placement="top">
          <span>{t("Request ID")}</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "objectFailureCount",
      headerName: t("Failure Objects"),
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count failed during syndication in SAP" arrow placement="top">
          <span>{t("Request ID")}</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "retryCount",
      headerName: t("Retry Count"),
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)" arrow placement="top">
          <span>{t("Request ID")}</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "schedulerStatus",
      headerName: t("Scheduler Status"),
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "action",
      headerName: t("Action"),
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
          <Tooltip title={t("Cancel")}>
            <IconButton
              aria-label="View Metadata"
            // onClick={() => {
            //   handleCancel(params.row.requestId, params.row.requestType);
            // }}
            >
              <DeleteForeverIcon
              />
            </IconButton>
          </Tooltip>
          <Tooltip title={t("Adhoc Syndication")}>
            <IconButton
              aria-label="View Metadata"
            onClick={() => {
              handleAdhocSync(params.row.requestId, params.row.requestType);
            }}
            >
              <RocketLaunchIcon
              />
            </IconButton>
          </Tooltip>
          </>
        );
      },
    },

  ];
  const priorityColumnsAttachment = [
    {
      field: "id",
      headerName: "",
      editable: false,
      flex: 1,
      hide: true,
      // width: "10%",
    },
    {
      field: "reqId",
      headerName: "Request ID",
      editable: false,
      flex: 1,
      // width: "20%",
    },
    {
      field: "module",
      headerName: "Module",
      editable: false,
      flex: 1,
      // width: "20%",
    },
    {
      field: "reqType",
      headerName: "Request Type",
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "scheduledBy",
      headerName: "Scheduled By",
      editable: false,
      flex: 1,
      // width: "15%",
    },
    {
      field: "scheduledOn",
      headerName: "Scheduled On",
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "totalObjects",
      headerName: "Total Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count scheduled for SAP syndication" arrow placement="top">
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "pendingObjects",
      headerName: "Pending Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count pending for SAP syndicated." arrow placement="top">
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "schedulerStatus",
      headerName: "Scheduler Status",
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      
    },
  ];
  let optionsForScheduler = ["Manage Scheduler", "SAP Scheduler", "Attachment Scheduler"]
  
const handleClick = (option, index) => {
    if (index !== 0) {
      // setSelectedIndex(index);
      setSchedulerType(optionsForScheduler[index])
      setFilterDataScheduler({
        module: 'All',
        requestType: 'All',
        status: 'Open'
      })
      setOpenButton(false);
      if (index === 1) {
        handleSchedulerChange("SAP Scheduler")
        setSchedulerType("SAP Scheduler");
        setSelectedTab("tab0"); // Reset tab selection
        //setTabValue("")
        setStatus("Open"); // Reset status
        // let newSelected = getStatusOptions()?.["Open"] || []
        // console.log(newSelected,"newSelecteddtata")
       // setSchedulerStatus([]); // Reset scheduler status
        
        fetchScheduledRequestswhentabChange("SAP Scheduler",0,["Scheduler - Failed", "Scheduler - Pending", "Scheduler - Partially Completed", "Scheduler - Paused"])
        //setSchedulerStatus(getStatusOptions()?.["Open"] || [])
       // setSchedulerStatus([]); // Reset scheduler status
        //fetchScheduledRequests("SAP Scheduler",0)
        setOpenScheduler(true);
        setTabValue(0);
      } else if (index === 2) {
        handleSchedulerChange("Attachment Scheduler")
        setSchedulerType("Attachment Scheduler");
        setSelectedTab(""); // Reset tab selection
        //setTabValue("")
        //setSchedulerStatus(getStatusOptions()?.["Open"] || []); // Reset status
       // let newSelected = getStatusOptions()?.["Open"] || []
       // console.log(newSelected,"newSelecteddtata")
       // setSchedulerStatus([]); // Reset scheduler status
        
       fetchScheduledRequestswhentabChange("Attachment Scheduler",1,["Scheduler - Failed", "Scheduler - Pending", "Scheduler - Partially Completed", "Scheduler - Paused"])
        // handleDownloadCreate();
        setOpenScheduler(true);
      }
    }
  };

  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    // setOpenButton(false);
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleCloseScheduler = () => {
    setStatus("")
    setSchedulerStatus([])
    setOpenScheduler(false);
    // setFilterDataScheduler({ module: 'All', requestType: 'All', status: 'Open' }); // Clear search fields
    setErrors({}); // Clear errors
    setPriorityRow([]);
  };
  const exportSchedulerData=()=>{
    let excelColumnSap = [];
    let excelColumnsAttachment = [];
    let exportRows = [];
    let exportColumns = [];
      priorityColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumnSap.push({ header: item.headerName, key: item.field });
        }
      });
      priorityColumnsAttachment.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumnsAttachment.push({ header: item.headerName, key: item.field });
        }
      });

      if (schedulerType === "SAP Scheduler") {
        exportRows = priorityRow.map((row) => ({
          ...row,
          scheduledOn: row.scheduledOn
            ? getFormatedTime(row.scheduledOn)
            : "",
        }));
        exportColumns = excelColumnSap;
      } else {
        exportRows = sapAttachmentRow.map((row) => ({
          ...row,
          scheduledOn: row.scheduledOn
            ? getFormatedTime(row.scheduledOn)
            : "",
        }));
        exportColumns = excelColumnsAttachment;
      }
      // if (schedulerType === "SAP Scheduler") {
      //   exportRows = priorityRow;
      //   exportColumns = excelColumnSap;
      // } else {
      //   exportRows = sapAttachmentRow;
      //   exportColumns = excelColumnsAttachment;
      // }
      
      let fileName =
    schedulerType === "SAP Scheduler"
      ? `SAP Scheduler Data-${moment(presentDate).format("DD-MMM-YYYY")}`
      : `Attachment Scheduler Data-${moment(presentDate).format("DD-MMM-YYYY")}`;
      saveExcel({
        fileName,
        columns: exportColumns,
        rows: exportRows,
      });
  }
  const getFormatedTime = (timestamp) => {
    const date = new Date(timestamp);
      
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(date.getDate()).padStart(2, '0');
    const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
  
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds} ${ampm}`;
  }
  const handleFilterChangeScheduler = (e) => {
    const { name, value } = e.target;
    setFilterDataScheduler({ ...filterDataScheduler, [name]: value });
  };
  const handleStatusChange = (event) => {
    setStatus(event.target.value);
    const { name, value } = event.target;
    setFilterDataScheduler({ ...filterDataScheduler, [name]: value });
    //setSchedulerStatus(getStatusOptions()?.[event.target.value] || []);
  };
  const handleSchedulerStatusChange = (event) => {
    setSchedulerStatus(event.target.value);
  };
  const getStatusOptions = () => {
     //console.log(schedulerType,tabValue,ShedularDependendDrodpDownData[schedulerType][selectedTab],"tabValuechangeData")
    //alert("schedulerType")
    if (!schedulerType) return null;
    if (schedulerType === "SAP Scheduler" && ! selectedTab) return null;

   
    
    return schedulerType === "Attachment Scheduler"
      ? ShedularDependendDrodpDownData[schedulerType]
      : ShedularDependendDrodpDownData[schedulerType][selectedTab];
  };
  const handleDateScheduler = (e) => {
    console.log("rgf",e);
    if (e !== null) {
      dispatch(
        commonFilterUpdate({
          module: "SchedulerManager",
          filterData: {
            ...rbSearchFormScheduler,
            createdOnScheduler: e,
          },
        })
      );
    }
  };
  const handleClearScheduler = () => {
    setStatus("")
    setSchedulerStatus([])
    setTabValue(0)
    setSelectedTab("tab0")
    setFilterDataScheduler({ module: '', requestType: '', status: '' });
  };
  const handleSearchScheduler = (tabValue,statusValue='') => {
    let hasError = false;
    const newErrors = {
      module: false,
      requestType: false,
      status: false,
    };

    if (!filterDataScheduler.module) {
      newErrors.module = true;
      hasError = true;
    }
    if (!filterDataScheduler.requestType) {
      newErrors.requestType = true;
      hasError = true;
    }
    if (!filterDataScheduler.status) {
      newErrors.status = true;
      hasError = true;
    }

    setErrors(newErrors);

    if (!hasError) {
      // Proceed with API call when no error
      console.log("All filters are selected. Proceeding with API call...");

      // Your API call logic here
      console.log("tabValuewhentabChange",status,tabValue)

      fetchScheduledRequests(schedulerType,tabValue);

      // if(tabValue == 0 ){
      //     fetchScheduledRequestswhentabChange(schedulerType,tabValue,["Scheduler - Failed", "Scheduler - Pending", "Scheduler - Partially Completed", "Scheduler - Paused"]);
      // }else{

      //   fetchScheduledRequests(schedulerType,tabValue,["Adhoc Syndication - Partially Completed","Adhoc Syndication - In Progress","Adhoc Syndication - Failed"]);    
      // }
      
    } else {
      console.log("Please select all mandatory fields.");
    }
  };
  useEffect(()=>{
    setSchedulerStatus(getStatusOptions()?.[status] || [])
  },[status,selectedTab,schedulerType])
  const handleChangeTab = (event, newValue) => {
    setStatus("Open")

    setTabValue(newValue)
    if(newValue == 0){
      setSelectedTab("tab0")
    }else{
      setSelectedTab("tab1")
    }
    if(newValue == 0){
      handleSearchSchedulertabChange(newValue,"Open")
    }else{
      handleSearchSchedulertabChange(newValue,"Open")
    } 
  };
  const handleSearchSchedulertabChange = (tabValue,statusValue='') => {
    let hasError = false;
    const newErrors = {
      module: false,
      requestType: false,
      status: false,
    };

    if (!filterDataScheduler.module) {
      newErrors.module = true;
      hasError = true;
    }
    if (!filterDataScheduler.requestType) {
      newErrors.requestType = true;
      hasError = true;
    }
    if (!filterDataScheduler.status) {
      newErrors.status = true;
      hasError = true;
    }

    setErrors(newErrors);

    if (!hasError) {
      if(tabValue == 0 ){
          fetchScheduledRequestswhentabChange(schedulerType,tabValue,["Scheduler - Failed", "Scheduler - Pending", "Scheduler - Partially Completed", "Scheduler - Paused"]);
      }else{

        fetchScheduledRequestswhentabChange(schedulerType,tabValue,["Adhoc Syndication - Partially Completed","Adhoc Syndication - In Progress","Adhoc Syndication - Failed"]);    
      }
      
    } else {
      customError("Please select all mandatory fields.");
    }
  };
  function a11yProps(index) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }
  const filteredColumns = priorityColumns;
  const filteredAttachmentColumns = filterDataScheduler.status === "Closed" 
  ? priorityColumnsAttachment.filter(col => col.field !== "action") 
: priorityColumnsAttachment;
  const handleRowUpdate = (updatedRows) => {
    // This function will receive the updated row data from the child component
    setPriorityRow(updatedRows);
  };
  const fetchSAPScheduler = () => {
    fetchScheduledRequests("SAP Scheduler")
  }
  const fetchScheduledRequests = (schedulerType,tabValue) => {
    //alert("fetchScheduledRequests")
    // Mapping selected status to corresponding statuses in the payload
    //console.log(newselectedSchdularStatus,schedulerStatus,"newselectedSchdularStatusData")
    // setIsLoadingCustom(true)
    const getStatusForPayload = (status) => {
      if(schedulerType === "Attachment Scheduler"){
        switch (status) {
          case "Open":
            return "Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed,Scheduler - Paused"
          case "Closed":
            return "Scheduler - Completed,Scheduler - Canceled"
          default:
            return ""; // Default case to handle any unexpected values
        }
      }
      else{
        if(tabValue===0){
          switch (status) {
            case "Open":
              return "Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed,Scheduler - Paused"
            case "Closed":
              return "Scheduler - Completed,Scheduler - Canceled"
            default:
              return ""; // Default case to handle any unexpected values
          }
        }
        else if(tabValue===1){
          switch (status) {
            case "Open":
              return "Adhoc Syndication - Partially Completed,Adhoc Syndication - In Progress,Adhoc Syndication - Failed"
            case "Closed":
              return "Adhoc Syndication - Completed,Adhoc Syndication - Canceled"
            default:
              return ""; // Default case to handle any unexpected values
          }
        }
      }
    };

    const getRequestTypeForPayload = (status) => {
      switch (status) {
        case "All":
          return "Create with Upload,Change with Upload,Extend with Upload"
        default:
          return filterDataScheduler.requestType; // Default case to handle any unexpected values
      }
    };
    const getRequestTypeForPayloadAttachment = (status) => {
      switch (status) {
        case "All":
          return "Create with Upload,Change with Upload,Extend with Upload"
        default:
          return filterDataScheduler.requestType; // Default case to handle any unexpected values
      }
    };

    const getModuleForPayload = (status) => {
      switch (status) {
        case "All":
          return "Material,Cost Center,Profit Center,CC-PC Combo,General Ledger"
        default:
          return filterDataScheduler.module; // Default case to handle any unexpected values
      }
    };


    const requestBody = {
      modules: getModuleForPayload(filterDataScheduler.module),
      requestTypes: getRequestTypeForPayload(filterDataScheduler.requestType),
      //statuses: getStatusForPayload(filterDataScheduler.status), // Map user-selected status to appropriate statuses
      statuses: schedulerStatus.join(','),
      fromDate:moment(rbSearchFormScheduler?.createdOnScheduler[0]).startOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      toDate:moment(rbSearchFormScheduler?.createdOnScheduler[1]).endOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
    };
    console.log("requestBody", requestBody);
    const requestBodyAttachment = {
      modules: getModuleForPayload(filterDataScheduler.module),
      requestTypes: getRequestTypeForPayloadAttachment(
        filterDataScheduler.requestType
      ),
      //statuses: getStatusForPayload(filterDataScheduler.status), // Map user-selected status to appropriate statuses
      
      statuses: schedulerStatus.join(','),
      fromDate:moment(rbSearchFormScheduler?.createdOnScheduler[0]).startOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      toDate:moment(rbSearchFormScheduler?.createdOnScheduler[1]).endOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
    };
    // const requestBodyForAttchment = {
    //   modules: getModuleForPayload(filterDataScheduler.module) ?? "",
    //   requestTypes: getRequestTypeForPayload(filterDataScheduler.requestType) ?? ""
    // }
    // const requestBodyForAttchment = {
    //   modules:getModuleForPayload(filterDataScheduler.module) ?? "",
    //   requestTypes:getRequestTypeForPayload(filterDataScheduler.requestType) ?? ""
    // }

    const hSuccess = (data) => {
      // let data = demoData;
      if (data.statusCode === 200) {
        console.log("prioritytaskssss", data);
        let priorityRequests = data?.data;
        // setIsLoadingCustom(false)
        let priorityRow = priorityRequests?.map((item) => {
          return {
            id: item?.SchedulerId,
            reqId: item?.RequestId,
            priority: item?.Priority,
            reqType: item?.RequestType,
            scheduledBy: item?.ReqScheduledBy,
            // scheduledOn: getFormatedTime(item?.ReqScheduledOn),
            scheduledOn: item?.ReqScheduledOn,
            totalObjects: item?.ObjectCount,
            pendingObjects: item?.PendingObjectsCount,
            retryCount: item?.RetryCount,
            module: item?.Module,
            objectSuccessCount: item?.ObjectsSuccessCount,
            objectFailureCount: item?.ObjectsFailureCount,
            updatedOn: item?.ReqUpdatedOn,
            schedulerStatus: item?.SchedulerStatus,
          };
        });
        console.log('prioritycheck', priorityRow)
        setPriorityRow(priorityRow);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed fetching Scheduled Requests. Please try again.");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
      }
    };

    const hSuccessSAPAttchment = (data) => {
      // let data2 = attachmentDemoData;
      if (data.statusCode === 200) {
        console.log("prioritytaskssss", data);
        let priorityRequests = data?.data;
        // Function to group by `Module` and sort by `RequestType`
        const groupAndSortByModuleAndRequestType = (data) => {
          if (!data || !Array.isArray(data)) return []; // Check if data is defined and is an array

          // Step 1: Group by `Module`
          const grouped = data.reduce((acc, item) => {
            const module = item.Module.toLowerCase(); // Case-insensitive grouping
            if (!acc[module]) {
              acc[module] = [];
            }
            acc[module].push(item);
            return acc;
          }, {});

          // Step 2: Sort each module group by `RequestType`
          for (const module in grouped) {
            grouped[module].sort((a, b) =>
              a.RequestType.localeCompare(b.RequestType)
            );
          }

          // Flatten the grouped data back into an array
          return Object.values(grouped).flat();
        };

        // Check if `priorityRequests.data` is available and map the sorted data
        const priorityRow = groupAndSortByModuleAndRequestType(
          priorityRequests
        ).map((item) => {
          return {
            id: item?.SchedulerId,
            reqId: item?.RequestId,
            reqType: item?.RequestType,
            scheduledBy: item?.ReqScheduledBy,
            scheduledOn: item?.ReqScheduledOn,
            // scheduledOn: getFormatedTime(item?.ReqScheduledOn),
            totalObjects: item?.ObjectCount,
            pendingObjects: item?.PendingObjectsCount,
            retryCount: item?.RetryCount,
            module: item?.Module,
            objectSuccessCount: item?.ObjectsSuccessCount,
            objectFailureCount: item?.ObjectsFailureCount,
            updatedOn: item?.ReqUpdatedOn,
            schedulerStatus: item?.SchedulerStatus,
          };
        });
        // let priorityRow = priorityRequests?.map((item) => {
        //   return {
        //     id: item?.SchedulerId,
        //     reqId: item?.RequestId,
        //     reqType: item?.RequestType,
        //     scheduledBy: item?.ReqScheduledBy,
        //     scheduledOn: item?.ReqScheduledOn,
        //     totalObjects: item?.ObjectCount,
        //     pendingObjects: item?.PendingObjectsCount,
        //     retryCount: item?.RetryCount,
        //     module: item?.Module,
        //     objectSuccessCount: item?.ObjectsSuccessCount,
        //     objectFailureCount: item?.ObjectsFailureCount,
        //     updatedOn: item?.ReqUpdatedOn,
        //     schedulerStatus: item?.SchedulerStatus,
        //   };
        // });
        console.log("prioritycheck", priorityRow);
        setSAPAttachmentRow(priorityRow);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Fetching Scheduled Requests. Try Once more."
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    const hErrorSAPAttchment = (error) => {
      console.log(error);
    };



    {
      if (schedulerType === "SAP Scheduler") {
        doAjax(
          `/${destination_Admin}${END_POINTS?.DATA?.FETCH_SCHEDULERS_IN_REQ_BENCH}`,
          "post",
          hSuccess,
          hError,
          requestBody
        );
      }
      else {
        doAjax(
          `/${destination_Admin}/scheduler/fetchPDFSchedulersInRequestBench`,
          "post",
          hSuccessSAPAttchment,
          hErrorSAPAttchment,
          requestBodyAttachment
        );
      }
    }

  };

  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  var payloadForScheduler = priorityRow?.map((y, index) => {
    return {
      SchedulerId: y?.id,
      RequestId: y?.reqId,
      RequestType: y?.reqType,
      Module: y?.module,
      Priority: y?.priority,
      ObjectCount: y?.totalObjects,
      ObjectsSuccessCount: y?.objectSuccessCount,
      ObjectsFailureCount: y?.objectFailureCount,
      PendingObjectsCount: y?.pendingObjects,
      ReqScheduledBy: y?.scheduledBy,
      ReqScheduledOn: y?.scheduledOn,
      ReqUpdatedOn: y?.updatedOn,
      // SchedulerStatus: iconClicked[y.id] ? "Scheduler - Canceled" : y.schedulerStatus,
      SchedulerStatus: y.schedulerStatus==="Scheduler - Pending" && iconClicked[y.id] ? "Scheduler - Paused" 
      : y.schedulerStatus==="Scheduler - Paused" && iconClicked[y.id] ? "Scheduler - Pending"
      : iconClicked[y.id] && (y.schedulerStatus!=="Scheduler - Canceled" || y.schedulerStatus!=="Scheduler - Completed") ? "Scheduler - Canceled"
      : y.schedulerStatus,
      RetryCount: y?.retryCount
    }
  })
  const onChangePriority = () => {
    handleCloseScheduler()
    setBlurLoading(true);
    const updatedPayloadMapping = payloadForScheduler.map((item) => ({
      ...item,
    }));
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage("Request IDs Scheduled for Syndication");
        // handleAttachment();
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIconClicked({}); // Reset iconClicked state after successful scheduling
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Scheduling Request. Retry Again");
        // setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Admin}${END_POINTS?.DATA?.UPDATE_PRIORITY_IN_REQ_BENCH}`,
      "post",
      hSuccess,
      hError,
      updatedPayloadMapping
    );
  };
  const onChangePriorityAttachementScheduler = () => {
    handleCloseScheduler()
    setBlurLoading(true);
    const updatedPayloadMapping = payloadForAttchement.map((item) => ({
      ...item,
    }));
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage("Attachments Scheduled for Syndication");
        // handleAttachment();
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Scheduling Attachments. Retry Again");
        // setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Admin}/scheduler/updateStatusForScheduledPDFRequests`,
      "post",
      hSuccess,
      hError,
      updatedPayloadMapping
    );
  };
  // CustomTabPanel.propTypes = {
  //   children: PropTypes.node,
  //   index: PropTypes.number.isRequired,
  //   value: PropTypes.number.isRequired,
  // };

  function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;
  
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  }
  const handleSchedulerChange = (event) => {
    setSchedulerType(event);
    setSelectedTab(""); // Reset tab selection
    //setTabValue("")
    setStatus("Open"); // Reset status
    //setSchedulerStatus(getStatusOptions()?.["Open"] || []);
   // setSchedulerStatus([]); // Reset scheduler status
  };
  const fetchScheduledRequestswhentabChange = (schedulerType,tabValue,newselectedSchdularStatus='') => {
    setIsLoadingCustom(true)
    // Mapping selected status to corresponding statuses in the payload
   // console.log(newselectedSchdularStatus,schedulerStatus,"newselectedSchdularStatusData")
    //alert("fetchScheduledRequestswhentabChange")
    // setIsLoadingCustom(true)
    const getStatusForPayload = (status) => {
      if(schedulerType === "Attachment Scheduler"){
        switch (status) {
          case "Open":
            return "Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed,Scheduler - Paused"
          case "Closed":
            return "Scheduler - Completed,Scheduler - Canceled"
          default:
            return ""; // Default case to handle any unexpected values
        }
      }
      else{
        if(tabValue===0){
          switch (status) {
            case "Open":
              return "Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed,Scheduler - Paused"
            case "Closed":
              return "Scheduler - Completed,Scheduler - Canceled"
            default:
              return ""; // Default case to handle any unexpected values
          }
        }
        else if(tabValue===1){
          switch (status) {
            case "Open":
              return "Adhoc Syndication - Partially Completed,Adhoc Syndication - In Progress,Adhoc Syndication - Failed"
            case "Closed":
              return "Adhoc Syndication - Completed,Adhoc Syndication - Canceled"
            default:
              return ""; // Default case to handle any unexpected values
          }
        }
      }
    };

    const getRequestTypeForPayload = (status) => {
      switch (status) {
        case "All":
          return "Create with Upload,Change with Upload,Extend with Upload"
        default:
          return filterDataScheduler.requestType; // Default case to handle any unexpected values
      }
    };
    const getRequestTypeForPayloadAttachment = (status) => {
      switch (status) {
        case "All":
          return "Create with Upload,Change with Upload,Extend with Upload"
        default:
          return filterDataScheduler.requestType; // Default case to handle any unexpected values
      }
    };

    const getModuleForPayload = (status) => {
      switch (status) {
        case "All":
          return "Material,Cost Center,Profit Center,CC-PC Combo,General Ledger"
        default:
          return filterDataScheduler.module; // Default case to handle any unexpected values
      }
    };


    const requestBody = {
      modules: getModuleForPayload(filterDataScheduler.module),
      requestTypes: getRequestTypeForPayload(filterDataScheduler.requestType),
      //statuses: getStatusForPayload(filterDataScheduler.status), // Map user-selected status to appropriate statuses
      statuses: newselectedSchdularStatus.length > 0 ?newselectedSchdularStatus.join(','):'',
      fromDate:moment(rbSearchFormScheduler?.createdOnScheduler[0]).startOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      toDate:moment(rbSearchFormScheduler?.createdOnScheduler[1]).endOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      // fromDate:
      //   moment(rbSearchFormScheduler?.createdOnScheduler[0])
      //     .utc() // Convert to UTC (Zulu Time)
      //     .format("YYYY-MM-DDTHH:mm:ss") ?? "",
      // toDate:
      //   moment(rbSearchFormScheduler?.createdOnScheduler[1])
      //     .utc() // Convert to UTC (Zulu Time)
      //     .format("YYYY-MM-DDTHH:mm:ss") ?? "",
    };
    console.log("requestBody", requestBody);
    const requestBodyAttachment = {
      modules: getModuleForPayload(filterDataScheduler.module),
      requestTypes: getRequestTypeForPayloadAttachment(
        filterDataScheduler.requestType
      ),
      //statuses: getStatusForPayload(filterDataScheduler.status), // Map user-selected status to appropriate statuses
      
      statuses: newselectedSchdularStatus.length > 0 ?newselectedSchdularStatus.join(',') :'',
      fromDate:moment(rbSearchFormScheduler?.createdOnScheduler[0]).startOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      toDate:moment(rbSearchFormScheduler?.createdOnScheduler[1]).endOf("day").utc().format("YYYY-MM-DDTHH:mm:ss") ?? "",
      // fromDate:
      //   moment(rbSearchFormScheduler?.createdOnScheduler[0])
      //     .utc() // Convert to UTC (Zulu Time)
      //     .format("YYYY-MM-DDTHH:mm:ss") ?? "",
      // toDate:
      //   moment(rbSearchFormScheduler?.createdOnScheduler[1])
      //     .utc() // Convert to UTC (Zulu Time)
      //     .format("YYYY-MM-DDTHH:mm:ss") ?? "",
    };
    // const requestBodyForAttchment = {
    //   modules: getModuleForPayload(filterDataScheduler.module) ?? "",
    //   requestTypes: getRequestTypeForPayload(filterDataScheduler.requestType) ?? ""
    // }
    // const requestBodyForAttchment = {
    //   modules:getModuleForPayload(filterDataScheduler.module) ?? "",
    //   requestTypes:getRequestTypeForPayload(filterDataScheduler.requestType) ?? ""
    // }

    const hSuccess = (data) => {
      setIsLoadingCustom(false)
      // let data = demoData;
      if (data.statusCode === 200) {
        console.log("prioritytaskssss", data);
        let priorityRequests = data?.data;
        // setIsLoadingCustom(false)
        let priorityRow = priorityRequests?.map((item) => {
          return {
            id: item?.SchedulerId,
            reqId: item?.RequestId,
            priority: item?.Priority,
            reqType: item?.RequestType,
            scheduledBy: item?.ReqScheduledBy,
            // scheduledOn: getFormatedTime(item?.ReqScheduledOn),
            scheduledOn: item?.ReqScheduledOn,
            totalObjects: item?.ObjectCount,
            pendingObjects: item?.PendingObjectsCount,
            retryCount: item?.RetryCount,
            module: item?.Module,
            objectSuccessCount: item?.ObjectsSuccessCount,
            objectFailureCount: item?.ObjectsFailureCount,
            updatedOn: item?.ReqUpdatedOn,
            schedulerStatus: item?.SchedulerStatus,
          };
        });
        console.log('prioritycheck', priorityRow)
        setPriorityRow(priorityRow);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed fetching Scheduled Requests. Please try again.");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
      }
    };

    const hSuccessSAPAttchment = (data) => {
      // let data2 = attachmentDemoData;
      if (data.statusCode === 200) {
        console.log("prioritytaskssss", data);
        let priorityRequests = data?.data;
        // Function to group by `Module` and sort by `RequestType`
        const groupAndSortByModuleAndRequestType = (data) => {
          if (!data || !Array.isArray(data)) return []; // Check if data is defined and is an array

          // Step 1: Group by `Module`
          const grouped = data.reduce((acc, item) => {
            const module = item.Module.toLowerCase(); // Case-insensitive grouping
            if (!acc[module]) {
              acc[module] = [];
            }
            acc[module].push(item);
            return acc;
          }, {});

          // Step 2: Sort each module group by `RequestType`
          for (const module in grouped) {
            grouped[module].sort((a, b) =>
              a.RequestType.localeCompare(b.RequestType)
            );
          }

          // Flatten the grouped data back into an array
          return Object.values(grouped).flat();
        };

        // Check if `priorityRequests.data` is available and map the sorted data
        const priorityRow = groupAndSortByModuleAndRequestType(
          priorityRequests
        ).map((item) => {
          return {
            id: item?.SchedulerId,
            reqId: item?.RequestId,
            reqType: item?.RequestType,
            scheduledBy: item?.ReqScheduledBy,
            scheduledOn: item?.ReqScheduledOn,
            // scheduledOn: getFormatedTime(item?.ReqScheduledOn),
            totalObjects: item?.ObjectCount,
            pendingObjects: item?.PendingObjectsCount,
            retryCount: item?.RetryCount,
            module: item?.Module,
            objectSuccessCount: item?.ObjectsSuccessCount,
            objectFailureCount: item?.ObjectsFailureCount,
            updatedOn: item?.ReqUpdatedOn,
            schedulerStatus: item?.SchedulerStatus,
          };
        });
        // let priorityRow = priorityRequests?.map((item) => {
        //   return {
        //     id: item?.SchedulerId,
        //     reqId: item?.RequestId,
        //     reqType: item?.RequestType,
        //     scheduledBy: item?.ReqScheduledBy,
        //     scheduledOn: item?.ReqScheduledOn,
        //     totalObjects: item?.ObjectCount,
        //     pendingObjects: item?.PendingObjectsCount,
        //     retryCount: item?.RetryCount,
        //     module: item?.Module,
        //     objectSuccessCount: item?.ObjectsSuccessCount,
        //     objectFailureCount: item?.ObjectsFailureCount,
        //     updatedOn: item?.ReqUpdatedOn,
        //     schedulerStatus: item?.SchedulerStatus,
        //   };
        // });
        console.log("prioritycheck", priorityRow);
        setSAPAttachmentRow(priorityRow);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Fetching Scheduled Requests. Try Once more."
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    const hErrorSAPAttchment = (error) => {
      console.log(error);
    };



    {
      if (schedulerType === "SAP Scheduler") {
        doAjax(
          `/${destination_Admin}/scheduler/fetchSchedulersInRequestBench`,
          "post",
          hSuccess,
          hError,
          requestBody
        );
      }
      else {
        doAjax(
          `/${destination_Admin}/scheduler/fetchPDFSchedulersInRequestBench`,
          "post",
          hSuccessSAPAttchment,
          hErrorSAPAttchment,
          requestBodyAttachment
        );
      }
    }

  };

  return (
    <div ref={ref_elementForExport}>
      {openAlldataDialog && (
        <ReusableDialogForAllData
          dialogState={false}
          openReusableDialog={handleAllDataDialogOpen}
          closeReusableDialog={handleAllDataDialogClose}
          // dialogTitle={messageDialogTitle}
          // dialogMessage={messageDialogMessage}
          rowsDataForPayload={rowsForDialog}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={t("OK")}
          // handleExtraButton={handleMessageDialogNavigate}
          // dialogSeverity={messageDialogSeverity}
        />
      )}
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={messageDialogMessage}
        alertType={alertType}
        handleSnackBarClose={handleSnackBarClose}
      />
      {openPopup && (
        <BifurcationPopup
          open={openPopup}
          onClose={() => setOpenPopup(false)}
          rowData={selectedRow}
          apiData={apiData}
          module={currentModule}
        />
      )}
      {openPopup && (
        <BifurcationPopup
          open={openPopup}
          onClose={() => setOpenPopup(false)}
          rowData={selectedRow}
          apiData={apiData}
          selectedRows={selectedRows}
          count={count}
          tableData={tableData}
          handleMassCancel={handleMassCancel}
        />
      )}
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={4}  xs={12}>
              <Typography variant="h3">
                <strong>{t("Request Management")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the list of Active Requests")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography  >Parent View</Typography>
                <Switch
                  checked={isChildView}
                  onChange={(_, checked) => setIsChildView(checked)}
                  color="primary"
                  inputProps={{ 'aria-label': 'toggle parent/child view' }}
                />
                <Typography>Child View</Typography>
              </Box>
            </Grid>
          </Grid>
          <Grid container>
            <Grid item md={12}>
            <StyledAccordion defaultExpanded={false} sx={{ mb: 2 }}>
              <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon
                    sx={{
                      fontSize: "1.25rem",
                      marginRight: 1,
                      color: colors.primary.main,
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: "0.875rem",
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    {t("Filter Request Bench")}
                  </Typography>
                </StyledAccordionSummary>
                <AccordionDetails sx={{ padding: 0 }}>
                <FilterContainer container>
                  {searchParammeters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                   .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                    .map((item, index) => {
                      return (
                        <React.Fragment key={index}>
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.INPUT && 
                            <Grid item md={2}>
                              <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                              <StyledTextField sx={font_Small} size="small" name={item?.MDG_MAT_JSON_FIELD_NAME} fullWidth onChange={handleInputChange} placeholder={t(`ENTER ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()} value={rbSearchForm?.[item?.MDG_MAT_JSON_FIELD_NAME]} />
                            </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.REQUESTTYPE &&
                               <Grid item md={2}>
                                <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                <AutoCompleteSimpleDropDown
                                  options={allOptions}
                                  value={typeof rbSearchForm?.requestType === "string" && rbSearchForm.requestType ? rbSearchForm.requestType.split("$^$") : []}
                                  onChange={(newValue) => {
                                    let tempRequestType;
                                    if (Array.isArray(newValue)) {
                                      if (newValue.length === 1) {
                                        tempRequestType = newValue[0];
                                      } else if (newValue.length > 1) {
                                        tempRequestType = newValue.join("$^$");
                                      } else {
                                        tempRequestType = "";
                                      }
                                    } else {
                                      tempRequestType = newValue || "";
                                    }

                                    let tempFilterData = {
                                      ...rbSearchForm,
                                      requestType: tempRequestType,
                                    };
                                    dispatch(
                                      commonFilterUpdate({
                                        module: "RequestBench",
                                        filterData: tempFilterData,
                                      })
                                    );
                                  }}
                                  placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                                />
                              </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.REQUESTPRIORITY &&
                              <Grid item md={2}>
                                <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                <AutoCompleteSimpleDropDown
                                  options={priortyOptions}
                                  value={selectedOptions}
                                  onChange={(newValue) => {
                                    handleChangePriority({
                                      target: {
                                        value: newValue,
                                      },
                                    });
                                  }}
                                  placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                                />
                              </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.CREATEDBYUSER && 
                            (
                              <>
                              {userRoles.includes(`${ROLES.SUPER_USER}`) ? (
                                <Grid item md={3}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      fullWidth
                                      size="small"
                                      value={selectedCreatedBy}
                                      multiple
                                      disableCloseOnSelect
                                      limitTags={1}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          t("No Data Available")
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setSelectedCreatedBy([]);
                                          // TO BE USED LATER
                                          //setselectedPreset([]);
                                          return;
                                        }

                                        if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                          handleSelectAllCreatedBy();
                                        } else {
                                          setSelectedCreatedBy(value);
                                        }
                                      }}
                                      options={createdByOptions?.length ? [{ code: "Select All", desc: "" }, ...createdByOptions] : createdByOptions ?? []}
                                      getOptionLabel={(option) => {
                                        if (option?.code) return `${option?.code} - ${option?.desc}` ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={<Checkbox checked={isCreatedBySelected(option) || (option?.code === "Select All" && selectedCreatedBy?.length === createdByOptions?.length)} />}
                                              label={
                                                <Typography style={{ fontSize: 12 }}>
                                                  {option?.desc ? (
                                                    <>
                                                      <strong>{option.code}</strong> - {option.desc}
                                                    </>
                                                  ) : (
                                                    <strong>{option.code}</strong>
                                                  )}
                                                </Typography>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderTags={(selected, getTagProps) => {
                                        const selectedOptionsText = selected.map((option) => `${option.code} `).join("<br />");
                                        return selected.length > 1 ? (
                                          <>
                                            <Chip
                                              sx={{
                                                height: 25,
                                                fontSize: "0.85rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                              label={`${selected[0]?.code}`}
                                              {...getTagProps({ index: 0 })}
                                            />
                                            <Chip
                                              sx={{
                                                height: 25,
                                                fontSize: "0.85rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                              label={`+${selected.length - 1}`}
                                              onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                              onMouseLeave={handlePopoverClose}
                                            />
                                            <Popover
                                              id={popoverId}
                                              open={isPopoverVisible}
                                              anchorEl={popoverAnchorEl}
                                              onClose={handlePopoverClose}
                                              anchorOrigin={{
                                                vertical: "bottom",
                                                horizontal: "center",
                                              }}
                                              transformOrigin={{
                                                vertical: "top",
                                                horizontal: "center",
                                              }}
                                              onMouseEnter={handleMouseEnterPopover}
                                              onMouseLeave={handleMouseLeavePopover}
                                              ref={popoverRef}
                                              sx={{
                                                "& .MuiPopover-paper": {
                                                  backgroundColor: "#f5f5f5",
                                                  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                                  borderRadius: "8px",
                                                  padding: "10px",
                                                  fontSize: "0.875rem",
                                                  color: "#4791db",
                                                  border: "1px solid #ddd",
                                                },
                                              }}
                                            >
                                              <Box
                                                sx={{
                                                  maxHeight: "270px",
                                                  overflowY: "auto",
                                                  padding: "5px",
                                                }}
                                                dangerouslySetInnerHTML={{ __html: popoverContent }}
                                              />
                                            </Popover>
                                          </>
                                        ) : (
                                          selected.map((option, index) => (
                                            <Chip
                                              sx={{
                                                height: 25,
                                                fontSize: "0.85rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                              label={`${option?.code}`}
                                              {...getTagProps({ index })}
                                            />
                                          ))
                                        );
                                      }}
                                      renderInput={(params) => (
                                        <Tooltip title={params.inputProps.value.length == 0 ? t("Type to search") : ""} arrow disableHoverListener={params.inputProps.value.length >= 1} placement="top">
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                                            onChange={(e) => {
                                              handleCreatedByInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              ) : (
                                <Grid item md={4}>
                                  <Typography sx={font_Small}>{t("Created By")}</Typography>

                                  <TextField fullWidth size="small" disabled={userData?.userRoles?.includes("Z4S:ALL:RTR:PC:PRF_CTR_MNT") ? false : true} value={userData?.userRoles?.includes("Z4S:ALL:RTR:PC:PRF_CTR_MNT") ? rbSearchForm?.createdBy : userData?.emailId} onChange={handleCreatedBy} placeholder={t("ENTER CREATED BY")} />
                                </Grid>
                              )}
                              </>
                            )
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.REQSTATUS && 
                            <Grid item md={2}>
                              <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                              <AutoCompleteSimpleDropDown
                                options={
                                  isChildView
                                    ? [...names.filter((name) => name !== "Select All").sort((a, b) => a.localeCompare(b))]
                                    : [...parentStatusOptions.filter((name) => name !== "Select All").sort((a, b) => a.localeCompare(b))]
                                }
                                value={rbSearchForm?.reqStatus}
                                onChange={(newValue) => {
                                  const event = {
                                    target: {
                                      name: "reqStatus",
                                      value: newValue,
                                    },
                                  };
                                  handleStatus(event);
                                }}
                                placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                              />
                            </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.DIVISION &&
                            <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <MaterialDropdown 
                              matGroup={dropDownData?.reqBenchDivision?.length ? [{ code: "Select All", desc: "" }, ...dropDownData?.reqBenchDivision] : dropDownData?.reqBenchDivision ?? []}
                              selectedMaterialGroup={selectedDivision} 
                              setSelectedMaterialGroup={setSelectedDivision} 
                              isDropDownLoading={DivisionDropdownLoading} 
                              placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()} 
                              onInputChange={handleDivInputChange} 
                              minCharacters={1} 
                            />
                          </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.CALENDAR && 
                            <Grid item md={2}>
                              <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                              <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
                                <LocalizationProvider dateAdapter={AdapterDateFns}>
                                  <DateRange
                                    handleDate={handleDate}
                                    // onChange={(e) => handleMatTypeChange(e)
                                    date={rbSearchForm?.createdOn}
                                  />
                                </LocalizationProvider>
                              </FormControl>
                            </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.MATERIALNOS && 
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <FormControl size="small" fullWidth>
                              <MaterialDropdown matGroup={materialOptions} selectedMaterialGroup={selectedMaterial} setSelectedMaterialGroup={setSelectedMaterial} isDropDownLoading={isDropDownLoading} placeholder={t(`ENTER ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()} onInputChange={handleMatInputChange} minCharacters={4} />
                            </FormControl>
                          </Grid>
                          }
                          {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.TEMPLATENAME && 
                            <Grid item md={2}>
                              <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                              <FormControl size="small" fullWidth>
                                <AutoCompleteSimpleDropDown
                                  options={tempNames.filter((name) => name !== "Select All").sort((a, b) => a.localeCompare(b))}
                                  value={rbSearchForm?.tempName || []}
                                  onChange={(newValue) => {
                                    handletempName({
                                      target: {
                                        value: newValue,
                                      },
                                    });
                                  }}
                                  placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                                />
                              </FormControl>
                            </Grid>
                          }
                        </React.Fragment>
                      )
                    })
                  }
                    </FilterContainer>
                    <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon sx={{ fontSize: "1rem" }} />}
                      onClick={handleClear}
                      sx={{ borderColor: colors.primary.main, color: colors.primary.main }}>
                        {t("Clear")}
                      </ActionButton>

                      <ActionButton
                        variant="contained"
                        size="small"
                        startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                        onClick={() => getFilter(activeTab)}
                        sx={{ backgroundColor: colors.primary.main }}
                      >
                        {t("Search")}
                      </ActionButton>
                    </ButtonContainer>
                  </AccordionDetails>
                </StyledAccordion>
              </Grid>
          </Grid>

          <Grid container>
            <Tabs
              value={activeTab}
              onChange={handleChange}
              variant="scrollable"
              
              sx={{
                background: "#FFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Tab
                disabled = {isLoading}
                  sx={{ fontSize: "12px", fontWeight: "700" }}
                  key={index}
                  label={
                   factor
                  }
                />
              ))}
            </Tabs>
          </Grid>

          <Grid container>
            {
                <Box  sx={{ width: "100%" }}>
                  {tabContents}
                </Box>
              }
          </Grid>
          <div ref={ref_elementForExport}>{SelectionSummaryfn()}</div>
          {
            // userData?.userRoles?.includes("Z4S:ALL:RTR:PC:PRF_CTR_MNT")?
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >{
                checkIwaAccess(iwaAccessData, "Request Bench", "Manage Scheduler") &&
                <ButtonGroup
                  variant="contained"
                  ref={anchorRef}
                  aria-label="split button"
                >
                 
                  <Button
                    size="small"
                    onClick={() => handleClick(optionsForScheduler[0], 0)}
                    sx={{ cursor: "default" }}
                  >
                    {t(optionsForScheduler[0])}
                  </Button>


                  <Button
                    size="small"
                    aria-controls={openButton ? "split-button-menu" : undefined}
                    aria-expanded={openButton ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggle}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup>
              }
                
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButton}
                  anchorEl={anchorRef.current}
                  placement={"top-end"}
                >
                  <Paper style={{ width: anchorRef.current?.clientWidth }}>
                    <ClickAwayListener onClickAway={handleCloseButton}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsForScheduler.slice(1)?.map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndex - 1}
                            onClick={() => handleClick(option, index + 1)}
                          >
                            {t(option)}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>

                {/* </NoMaxWidthTooltip> */}
              </BottomNavigation>
            </Paper>
            // :""
          }
          {/* {
            showBtmNav && */}
        </Stack>
        {isDeleteDialogVisible?.isVisible && (
          <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteForeverOutlined size="small" color="error" sx={{ fontSize: "20px" }} />} Title={"Cancel Request!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
            <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.CANCEL_MESSAGE)}</DialogContent>
          <CustomDialog
            isOpen={isDeleteDialogVisible?.isVisible}
            titleIcon={
              <DeleteForeverOutlined
                size="small"
                color="error"
                sx={{ fontSize: "20px" }}
              />
            }
            Title={"Cancel Request!"}
            handleClose={() =>
              setIsDeleteDialogVisible({
                ...isDeleteDialogVisible,
                isVisible: false,
              })
            }
          >
            <DialogContent sx={{ mt: 2 }}>
              {DIALOUGE_BOX_MESSAGES.CANCEL_MESSAGE}
            </DialogContent>
            <DialogActions>
              <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
                {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
              </Button>
              <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
                {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
              </Button>
            </DialogActions>
          </CustomDialog>
            </CustomDialog>
        )}
        
        {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
        <Dialog
          open={openScheduler}
          onClose={handleCloseScheduler}
          fullWidth
          maxWidth="xlg"
          PaperProps={{
            sx: {
              borderRadius: 3,
              boxShadow: 6,
              // Ensure dialog doesn't interfere with tooltip z-index
              zIndex: 1300,
            },
          }}
          // Add this to prevent tooltip clipping
          disablePortal={false}
        >
          {/* Header */}
          <DialogTitle
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              p: 2,
              borderBottom: "1px solid #e0e0e0",
              background: "#ececff",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <ScheduleIcon sx={{ color: colors.primary.main, fontSize: 32 }} />
              <Box>
                <Typography variant="h5" fontWeight={700}>
                  {schedulerType === "SAP Scheduler" ? t("SAP") : t("Attachment")} {t("Scheduler Manager")}
                </Typography>
                <Typography variant="body2" color="text.secondary" mt={0.5}>
                  {t("This view manages the scheduled")}{" "}
                  {schedulerType === "SAP Scheduler" ? t("Object") : t("Attachment")} {t("requests")}.
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: "flex", gap: 1 }}>
              <Tooltip title="Export Table">
                <IconButton onClick={exportSchedulerData}>
                  <ReusableIcon iconName="IosShare" />
                </IconButton>
              </Tooltip>
              <IconButton onClick={handleCloseScheduler}>
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          {/* Content */}
          <DialogContent sx={{ 
              px: 1, 
              py: 1, 
              marginTop: 2,
              maxHeight: '70vh',
              overflow: 'auto',
              overflow: 'visible'
            }}>
            {/* Filter Section */}
            <Accordion defaultExpanded={false} sx={{ mb: 2,marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)', }}>
              <StyledAccordionSummary
                expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                aria-controls="scheduler-filter-content"
                id="scheduler-filter-header"
              >
                <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
                <Typography
                  sx={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: colors.primary.dark,
                  }}
                >
                  {t("Filter Search Parameters")}
                </Typography>
              </StyledAccordionSummary>
              <AccordionDetails sx={{ padding: 0 }}>
                <FilterContainer container>
                  {/* Each Grid item cleaned up */}
                  <Grid item md={2}>
                    <LabelTypography fontWeight={500} gutterBottom>{t("Module")}<span style={{ color: colors.error.darkRed, marginLeft: '2px' }}>*</span></LabelTypography>
                    <FormControl fullWidth size="small" error={errors?.module}>
                      <Select
                        name="module"
                        value={filterDataScheduler.module}
                        onChange={handleFilterChangeScheduler}
                      >
                        {["All", "Cost Center", "Profit Center", "General Ledger", "CC-PC Combo", "Material"].map((val) => (
                          <MenuItem key={val} value={val}>{val}</MenuItem>
                        ))}
                      </Select>
                      {errors.module && (
                        <Typography variant="caption" color="error">
                          {t("Please select a Module")}.
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Request Type */}
                  <Grid item md={2}>
                    <LabelTypography fontWeight={500} gutterBottom>{t("Request Type")}<span style={{ color: colors.error.darkRed, marginLeft: '2px' }}>*</span></LabelTypography>
                    <FormControl fullWidth size="small" error={errors.requestType}>
                      <Select
                        name="requestType"
                        value={filterDataScheduler.requestType}
                        onChange={handleFilterChangeScheduler}
                      >
                        {["All", "Create with Upload", "Change with Upload", "Extend with Upload"]
                          .concat(schedulerType !== "SAP Scheduler" ? ["Temporary Mass Change"] : [])
                          .map((val) => (
                            <MenuItem key={val} value={val}>{val}</MenuItem>
                        ))}
                      </Select>
                      {errors.requestType && (
                        <Typography variant="caption" color="error">
                          {t("Please select a Request Type")}.
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Status */}
                  <Grid item md={2}>
                    <LabelTypography fontWeight={500} gutterBottom>{t("Status")}<span style={{ color: colors.error.darkRed, marginLeft: '2px' }}>*</span></LabelTypography>
                    <FormControl fullWidth size="small" error={errors.status}>
                      <Select name="status" value={filterDataScheduler.status} onChange={handleStatusChange}>
                        <MenuItem value="Open">Open</MenuItem>
                        <MenuItem value="Closed">Closed</MenuItem>
                      </Select>
                      {errors.status && (
                        <Typography variant="caption" color="error">
                          {t("Please select a Status")}.
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Scheduler Status (Multi-Select) */}
                  <Grid item md={2}>
                    <LabelTypography fontWeight={500} gutterBottom>{t("Scheduler Status")}</LabelTypography>
                    <FormControl fullWidth size="small">
                      <Select
                        multiple
                        value={schedulerStatus}
                        onChange={handleSchedulerStatusChange}
                        renderValue={(selected) => selected.join(", ")}
                      >
                        {getStatusOptions()?.[status]?.map((option) => (
                          <MenuItem key={option} value={option}>
                            <Checkbox checked={schedulerStatus.indexOf(option) > -1} />
                            <ListItemText primary={option} />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Date Range */}
                  <Grid item md={2}>
                    <LabelTypography fontWeight={500} gutterBottom>{t("Scheduled On")}</LabelTypography>
                    <FormControl fullWidth>
                      <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <DateRange
                          handleDate={handleDateScheduler}
                          date={selectedDateRangeScheduler}
                        />
                      </LocalizationProvider>
                    </FormControl>
                  </Grid>
                </FilterContainer>
                <ButtonContainer>
                  <ActionButton
                    variant="outlined"
                    size="small"
                    startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                    onClick={handleClearScheduler}
                    sx={{ borderColor: colors.primary.main, color: colors.primary.main }}
                  >
                    {t("Clear")}
                  </ActionButton>
                  <ActionButton
                    variant="contained"
                    size="small"
                    startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                    onClick={() => handleSearchScheduler(tabValue)}
                    sx={{ backgroundColor: colors.primary.main }}
                  >
                    {t("Search")}
                  </ActionButton>
                </ButtonContainer>
              </AccordionDetails>
            </Accordion>

            {/* Tab Section */}
            {schedulerType === "SAP Scheduler" ? (
              <>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={tabValue} onChange={handleChangeTab} variant="standard">
                  <Tab label={t("Scheduled Syndication")} {...a11yProps(0)} iconPosition="start" icon={<SyncIcon sx={{ fontSize: '1rem' }} />} />
                  <Tab label={t("Adhoc Syndication")} {...a11yProps(1)} iconPosition="start" icon={<RocketLaunchOutlined sx={{ fontSize: '1rem' }} />} />
                </Tabs>
                </Box>
                <Divider />

                <CustomTabPanel value={tabValue} index={0}>
                  <CustomDataGrid
                    columns={filteredColumns}
                    isLoading={isLoadingCustom}
                    row={priorityRow ?? []}
                    onRowUpdate={handleRowUpdate}
                    iconClicked={iconClicked}
                    setIconClicked={setIconClicked}
                    selectionType={"SAPScheduler"}
                    showDragIcon={schedulerType === "SAP Scheduler" && filterDataScheduler.status === "Open"}
                    fetchSAPScheduler={fetchSAPScheduler}
                  />
                </CustomTabPanel>

                <CustomTabPanel value={tabValue} index={1}>
                  <CustomDataGrid
                    columns={filteredColumns}
                    isLoading={isLoadingCustom}
                    row={priorityRow ?? []}
                    onRowUpdate={handleRowUpdate}
                    iconClicked={iconClicked}
                    setIconClicked={setIconClicked}
                    selectionType={"AdhocScheduler"}
                    showDragIcon={false}
                    fetchSAPScheduler={fetchSAPScheduler}
                  />
                </CustomTabPanel>
              </>
            ) : (
              <CustomDataGrid
                columns={filteredAttachmentColumns}
                isLoading={isLoadingCustom}
                row={sapAttachmentRow ?? []}
                onRowUpdate={handleRowUpdate}
                iconClicked={iconClicked}
                setIconClicked={setIconClicked}
                selectionType={"AttachmentScheduler"}
                showDragIcon={false}
              />
            )}
          </DialogContent>

          {/* Actions */}
          {(filterDataScheduler.status === "Open" && (
            (schedulerType === "SAP Scheduler" && tabValue === 0 && priorityRow?.length > 0) ||
            (schedulerType === "Attachment Scheduler" && sapAttachmentRow?.length > 0)
          )) && (
            <DialogActions sx={{ px: 3, py: 2, borderTop: "1px solid #eee" }}>
              <Button onClick={handleCloseScheduler}>{t("Cancel")}</Button>
              <Button
                variant="contained"
                className="button_primary--normal"
                onClick={() =>
                  schedulerType === "SAP Scheduler"
                    ? onChangePriority()
                    : onChangePriorityAttachementScheduler()
                }
              >
                {t("Submit")}
              </Button>
            </DialogActions>
          )}
        </Dialog>
      </div>
    </div>
  );
};

export default RequestBench;
