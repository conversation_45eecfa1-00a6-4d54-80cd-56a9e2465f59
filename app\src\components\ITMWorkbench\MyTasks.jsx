import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import configData from "../../data/configData";
import { getLanguageTranslationData, userPermissions } from "../../data/propData";
import { useNavigate } from "react-router-dom";
import { setTaskData, setUserDetails } from "../../app/userManagementSlice";
import { setHistoryPath } from "../../app/utilitySlice";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_CostCenter, destination_GeneralLedger, destination_MaterialMgmt, destination_ProfitCenter_Mass, destination_BankKey, destination_IDM } from "../../destinationVariables";
import { setIwmMyTask } from "../../app/initialDataSlice";
import { clearGeneralLedger } from "../../app/generalLedgerTabSlice";
import { clearPaginationData } from "@app/paginationSlice"
import { setChangeFieldRows } from "@app/payloadSlice";
import { API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, INFO_MESSAGES, LOCAL_STORAGE_KEYS } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import ReusableDialog from "@components/Common/ReusableDialog";
import { setChangeFieldRowsDisplay, setFCRows } from "../../app/payloadslice";
import { getRequestTypeFromId, setLocalStorage } from "@helper/helper";
import useDisplayData from "@hooks/useDisplayDataDto";
import { useSnackbar } from "@hooks/useSnackbar";
import { APP_END_POINTS } from "../../constant/appEndPoints";

export default function MyTasks() {
  let userData = useSelector((state) => state.userManagement.userData);
  const task = useSelector((state) => state.userManagement.taskData);
  const { customLog } = useLogger();
  const langSelected = useSelector((state) => state.appSettings.language);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const [rowData, setRowData] = useState({});
  const [userListBySystem, setUserListBySystem] = useState(null);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const {getDisplayData} = useDisplayData();
  const { showSnackbar } = useSnackbar();

  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      // {
      //   Description: "",
      //   Name: "IWAServices",
      //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
      // },
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };

  const userPrefData = {
    DateTimeFormat: {
      dateTimeFormat: "DD MMM YYYY||HH:mm",
      timeZone: "Asia/Calcutta",
    },
  };

  
  const forwardedToUsers = (forwardedTo) => {
   
    const payload = {
        eventId:"TASK_FORWARDING",
        taskName:forwardedTo?.forwardedTasks.map(user => user.taskDesc).join(','),
        requestId: forwardedTo?.forwardedTasks.map(user => `${user.ATTRIBUTE_1}`).join(','),
        recipientGroup: forwardedTo?.recipientUsers.map(user => user.ownerId).join(','),
        flowType:forwardedTo?.forwardedTasks.map(user => user.ATTRIBUTE_2).join(',')
     
    }
   
     const hSuccess = (res) => {
      customLog(res)
        };
  doAjax(
          `/${destination_MaterialMgmt}/mail/sendMail`,
          "post",
          hSuccess,
          payload
        );
  }

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const onTaskClick = async (task) => {
    setLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, task);
    try {
      if ((task?.taskNature === "Single-User") || (task?.taskNature !== "Single-User" && task?.itmStatus !== "Open")) {
        dispatch(setTaskData(task));
        if (task?.processDisplayName === "Material") {
          if(!task?.ATTRIBUTE_1){
            showSnackbar(INFO_MESSAGES.FETCHING_REQUEST_ID,'info')
          }else if(!task?.ATTRIBUTE_2){
            showSnackbar(INFO_MESSAGES.FETCHING_REQUEST_TYPE,'info')
          }
          else if(!task?.ATTRIBUTE_5){
            showSnackbar(INFO_MESSAGES.FETCH_ROLE,'info')
          }
          else{
            const displayResponse = await getDisplayData(task?.ATTRIBUTE_1,task?.ATTRIBUTE_2,null,task,null);
            if(displayResponse?.statusCode === API_CODE.STATUS_200){
              navigate(`/requestBench/createRequest?RequestId=${task?.ATTRIBUTE_1 || task?.requestId}&RequestType=${task?.ATTRIBUTE_2 || getRequestTypeFromId(task?.requestId)}`);
            }
          }
          
        }
        if (task?.processDisplayName === "Profit Center Group") {
          navigate(`/${APP_END_POINTS?.CREATE_PCG}?RequestId=${task?.ATTRIBUTE_1}&RequestType=${task?.ATTRIBUTE_2}`);
        }
        if (task?.processDisplayName === "Profit Center") {
         
          navigate(`/requestBench/ProfitCenterRequestTab?RequestId=${task?.ATTRIBUTE_1?.slice(6)}&RequestType=${task?.ATTRIBUTE_2}&reqBench=${true}`);
        }
        if (task?.processDisplayName === "General Ledger") {
         
          navigate(`/requestBench/GeneralLedgerRequestTab?RequestId=${task?.ATTRIBUTE_1?.slice(8)}&RequestType=${task?.ATTRIBUTE_2}&reqBench=${true}`);
        }
        if (task?.processDisplayName === "Cost Center") {
       
          navigate(`/requestBench/CostCenterRequestTab?RequestId=${task?.ATTRIBUTE_1?.slice(6)}&RequestType=${task?.ATTRIBUTE_2}&reqBench=${true}`);
        }
        dispatch(setHistoryPath({ url: window.location.pathname, module: "ITMWorkbench" }));
      } else {
        setMessageDialogMessage("Kindly claim the task before proceeding");
        setMessageDialogTitle("Claim Task");
        setMessageDialogSeverity("info");
        handleMessageDialogClickOpen();
      }
    } catch (error) {
      customLog(ERROR_MESSAGES?.ERROR_SET_ROLE);
    }
  };
  

  const fetchFilterViewList = () => {
    console.log("fetchFilterView");
  };
  const clearFilterView = () => {
    console.log("clearFilterView");
  };
  // Might use later or will remove after a week
  // const fetchUserRawData = () => {
  //   doAjax(`/${destination_MaterialMgmt}/workflow/fetchAllIASUsersForward`, "get", (resData) => {
  //     var tempData = resData.data;
  //     var tempUserData = tempData?.map((udata) => {
  //       return { ...udata, userId: udata?.emailId };
  //     });
  //     var finalData = { ...resData, data: tempUserData };
  //     setUserRawData(finalData);
  //     setUserListBySystem({ MDG: [...finalData.data] });
  //   });
  // };

  // const fetchUserGroupRawData = () => {
  //   doAjax(`/${destination_MaterialMgmt}/workflow/fetchAllIASGroups`, "get", (resData) => {
  //     var tempData = resData.data;
  //     var tempGroupData = tempData?.map((gData) => {
  //       return { ...gData, groupName: gData?.name };
  //     });
  //     var finalData = { ...resData, data: tempGroupData };
  //     setUserGroupRawData(finalData);
  //   });
  // };

  const onActionComplete = (successFlag, taskPayload) => {
    console.log("Success flag.", successFlag);
    console.log("Task Payload.", taskPayload);
  };

  useEffect(() => {
    setIwmMyTask({});
    dispatch(clearPaginationData())
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
    dispatch(setTaskData({}));
    dispatch(setFCRows([]));
    //NOTE: Might use later
    // fetchUserRawData();
    // fetchUserGroupRawData();
  }, []);
  return (
    <div style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }} className={"workspaceOverride"}>
       <WorkspaceComponent
       token={"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.rF_pXfhcgsckmiMODzFhrB7bKQJI3-PVRf7xbW7lQu0xwPT7pR-4_djP3XU_HsHJjGBRKVqXIlt472mle_2cyHrcbH4i65rY0j2SmnR9leI9Q1r7Puyo5PU3S1wn2fHeKgzwV1mB5aCzEvBAblAfUM-_j_WY4GbkL6XeSSp-0SBbW1JVeKz8_E9rgenQE8t8yK7EpKiQjB1u2jMLjllpMlFT3mjO2JMkVpWXFx1JBmxf25ioOAkbm2KW5jDBoclU6fP7x3WefGv3A-LYEBM1fQ9Fas2K9-vDDK-l1CvhcPe1vvfMKQHaoUg31T_E-yaFb9ImZQSAejtE_V2EkZsnDw"}
       configData={configData}
       destinationData={DestinationConfig}
       userData={{ ...userData, user_id: userData?.emailId }}
       userPreferences={userPrefData} //Not needed check
       userPermissions={userPermissions}
       userList={{}}
       groupList={{}}
       languageTranslationData={getLanguageTranslationData(langSelected)}
       userListBySystem={userListBySystem}
       useWorkAccess={
         applicationConfig.environment === "localhost" ? true : false
       } 
       useConfigServerDestination={
         applicationConfig.environment === "localhost" ? true : false
       }
       // inboxTypeKey={task?.inboxTypeKey}
       // workspaceLabel={task?.workspaceLabel}
       inboxTypeKey={"MY_TASKS"}
       workspaceLabel={"Open Tasks"}
       workspaceFiltersByAPIDriven={false}
       subInboxTypeKey={null}
       cachingBaseUrl={baseUrl_ITMJava}
       onTaskClick={onTaskClick}
       // onTaskLinkClick={onTaskLinkClick} 
       onActionComplete={onActionComplete}
       selectedFilterView={null}
       isFilterView={false}
       fetchFilterViewList={fetchFilterViewList}
       savedFilterViewData={[]}
       clearFilterView={clearFilterView}
       filterViewList={[]}
       selectedTabId={null}
       forwardTaskData = {forwardedToUsers}
       userProcess={[]}
       // handleCustomActionApis={handleCustomActionApis}   prev comment
     />

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleOk={handleOk}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />
    </div>
  );
}
