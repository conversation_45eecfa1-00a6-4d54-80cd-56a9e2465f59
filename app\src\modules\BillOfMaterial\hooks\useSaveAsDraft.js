import { useCallback } from "react";
import { useSelector } from "react-redux";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../../destinationVariables";
import { useSnackbar } from "@hooks/useSnackbar";

const useSaveAsDraft = () => {
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const bomRows = useSelector((state) => state.bom.bomRows);
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);
  const { showSnackbar } = useSnackbar();

  const handleSaveAsDraft = useCallback(
    (actionType) => {
      const payload = bomRows.map((row) => {
        const rowId = row.id;
        const materialTabRows = tabFieldValues[rowId]?.Material || [];
        const documentTabRows = tabFieldValues[rowId]?.Document || [];

        return {
          BOMHeaderId: 0,
          Material: row.material || "",
          Plant: row.plant || "",
          TemplateName: "",
          MassCreationId: "",
          MassEditId: "",
          MassChildCreationId: "",
          MassChildEditId: "",
          ChangeFields: "",
          ToBOMitem: materialTabRows.map((matRow) => ({
            BOMItemId: matRow.BOMItemId || 0,
            ItemCateg: matRow.ItemCateg || "",
            ItemNo: matRow.ItemNo || "",
            Component: matRow.Component || "",
            CompQty: matRow.CompQty || "",
            CompUnit: matRow.CompUnit || "",
            FixedQty: matRow.FixedQty || "",
            ItemText1: matRow.ItemText1 || "",
            ItemText2: matRow.ItemText2 || "",
            Sortstring: matRow.Sortstring || "",
            RelCost: matRow.RelCost || "",
            RelEngin: matRow.RelEngin || "",
            RelPmaint: matRow.RelPmaint || "",
            RelProd: matRow.RelProd || "",
            RelSales: matRow.RelSales || "",
            SparePart: matRow.SparePart || "",
            MatProvis: matRow.MatProvis || "",
            BulkMat: matRow.BulkMat || "",
            RecAllowd: matRow.RecAllowd || "",
            CompScrap: matRow.CompScrap || "",
            OpScrap: matRow.OpScrap || "",
            OpNetInd: matRow.OpNetInd || "",
            DistrKey: matRow.DistrKey || "",
            ExplType: matRow.ExplType || "",
            Spproctype: matRow.Spproctype || "",
            Supplyarea: matRow.Supplyarea || "",
            IssueLoc: matRow.IssueLoc || "",
            LeadTime: matRow.LeadTime || "",
            OpLeadTm: matRow.OpLeadTm || "",
            OpLtUnit: matRow.OpLtUnit || "",
            CoProduct: matRow.CoProduct || "",
            DisconGrp: matRow.DisconGrp || "",
            FollowGrp: matRow.FollowGrp || "",
            AiGroup: matRow.AiGroup || "",
            AiStrateg: matRow.AiStrateg || "",
            AiPrio: matRow.AiPrio || "",
            UsageProb: matRow.UsageProb || "",
            Refpoint: matRow.Refpoint || "",
            PmAssmbly: matRow.PmAssmbly || "",
            CostElem: matRow.CostElem || "",
            DelivTime: matRow.DelivTime || "",
            GrpTime: matRow.GrpTime || "",
            MatGroup: matRow.MatGroup || "",
            Price: matRow.Price || "",
            PriceUnit: matRow.PriceUnit || "",
            Currency: matRow.Currency || "",
            PurchGrp: matRow.PurchGrp || "",
            PurchOrg: matRow.PurchOrg || "",
            Vendor: matRow.Vendor || "",
            VsiNo: matRow.VsiNo || "",
            VsiQty: matRow.VsiQty || "",
            VsiSize1: matRow.VsiSize1 || "",
            VsiSize2: matRow.VsiSize2 || "",
            VsiSize3: matRow.VsiSize3 || "",
            VsiSzunit: matRow.VsiSzunit || "",
            VsiFormul: matRow.VsiFormul || "",
            Document: matRow.Document || "",
            DocType: matRow.DocType || "",
            DocPart: matRow.DocPart || "",
            DocVers: matRow.DocVers || "",
            Class: matRow.Class || "",
            ClassType: matRow.ClassType || "",
            ResItmCt: matRow.ResItmCt || "",
            SelCond: matRow.SelCond || "",
            ReqdComp: matRow.ReqdComp || "",
            MultSelec: matRow.MultSelec || "",
            RelHlconf: matRow.RelHlconf || "",
            CadInd: matRow.CadInd || "",
            ItmIdent: matRow.ItmIdent || "",
            ItemGuid: matRow.ItemGuid || "",
            ValidFrom: matRow.ValidFrom || "",
            ChangeNo: matRow.ChangeNo || "",
            Identifier: matRow.Identifier || "",
            UnloadPt: matRow.UnloadPt || "",
            GrRcpt: matRow.GrRcpt || "",
            SegmentValue: matRow.SegmentValue || "",
            FshCriticalComp: matRow.FshCriticalComp || "",
            FshCriticalLevel: matRow.FshCriticalLevel || "",
            Cufactor: matRow.Cufactor || "",
          })),
          ToBomdecription: [],
          ToBomdocumentation: documentTabRows.map((docRow) => ({
            BOMDocumentationId: docRow.BOMDocumentationId || 0,
            ObjectId: docRow.ObjectId || "",
            Identifier: docRow.Identifier || "",
            BomNo: docRow.BomNo || "",
            ItemNode: docRow.ItemNode || "",
            ItemCount: docRow.ItemCount || "",
            DepIntern: docRow.DepIntern || "",
            DepExtern: docRow.DepExtern || "",
            Language: docRow.Language || "",
            LineNo: docRow.LineNo || "",
            TxtForm: docRow.TxtForm || "",
            TxtLine: docRow.TxtLine || "",
            LanguageIso: docRow.LanguageIso || "",
          })),
          Torequestheaderdata: {
            RequestId: createdRequestId || "",
            ReqCreatedBy: payloadFields?.ReqCreatedBy || "",
            ReqCreatedOn: payloadFields?.ReqCreatedOn || "",
            ReqUpdatedOn: payloadFields?.ReqUpdatedOn || "",
            RequestType: payloadFields?.RequestType?.code || "",
            RequestPrefix: payloadFields?.RequestPrefix || "",
            RequestPriority: payloadFields?.RequestPriority?.code || "",
            RequestDesc: payloadFields?.RequestDesc || "",
            RequestStatus: payloadFields?.RequestStatus || "",
            TemplateName: payloadFields?.TemplateName || "",
            FieldName: payloadFields?.FieldName?.join("$^$") || "",
            Region: payloadFields?.Region?.code || "",
            filterDetails: payloadFields?.filterDetails || "",
            SapSystem: payloadFields?.SapSystem || "",
            IsBifurcated: payloadFields?.IsBifurcated || "",
            IncompleteChildTasks: payloadFields?.IncompleteChildTasks || "",
          },
          Tochildrequestheaderdata: {
            ChildRequestId: "",
            MaterialGroupType: "",
            TaskId: "",
            Comments: "",
            TotalIntermediateTasks: 0,
            IntermediateTaskCount: 0,
            ReqCreatedBy: "",
            ReqCreatedOn: "",
            ReqUpdatedOn: "",
            RequestType: "",
            RequestPrefix: "",
            RequestDesc: "",
            RequestPriority: "",
            RequestStatus: "",
            CurrentLevel: 0,
            CurrentLevelName: "",
            ParticularLevel: 0,
            TaskName: "",
            ApproverGroup: "",
            Index: 0,
            TaskCreatedOn: "",
            DueDate: "",
            IsScheduled: false,
          },
          Usage: row.bomUsage || "",
          Alternative: row.altBom || "",
          ValidFrom: row.validFrom || "",
          ValidTo: row.validTo || "",
          ChangeNumber: "",
          RevisionLevel: "",
          BaseQuan: "",
          BaseUnit: "",
          BomStatus: "",
          AltText: "",
          Laboratory: "",
          DeleteInd: "",
          BomText: "",
          BomGroup: "",
          AuthGroup: "",
          CadInd: "",
          IdGuid: "",
          BomNo: "",
        };
      });

      doAjax(
        `/${destination_BOM}/massAction/createBOMSaveAsDraft`,
        "post",
        (data) => {
          showSnackbar("BOM saved as draft successfully", "success");
        },
        (err) => {
          showSnackbar("Error occurred while saving BOM as draft", "error");
        },
        payload
      );
    },
    [payloadFields, bomRows, tabFieldValues, createdRequestId]
  );

  return handleSaveAsDraft;
};

export default useSaveAsDraft;
