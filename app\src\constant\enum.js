export const API_CODE = {
  STATUS_200: 200,
  STATUS_202: 202,
  STATUS_300: 300,
  STATUS_429: 429,
  STATUS_204: 204,
  STATUS_0: 0,
  STATUS_414: 414,
  STATUS_423: 423,
};

export const MATERIAL_TABLE = {
  MATERIALNUMBER: "materialNumber",
  GL<PERSON><PERSON>LMATERIALDESCRIPTION: "globalMaterialDescription",
  MATERIALTYPE: "materialType",
  INCLUDED: "included",
  VIEWS: "views",
};

export const MATERIAL_VIEWS = {
  BASIC_DATA: "Basic Data",
  SALES: "Sales",
  MRP: "MRP",
  PURCHASING: "Purchasing",
  ACCOUNTING: "Accounting",
  CLASSIFICATION: "Classification",
  PLANT_STOCKS: "Plant stocks",
  PRODUCTION: "Production",
  QUALITY_MANAGEMENT: "Quality management",
  WAREHOUSE_MANAGEMENT: "Warehouse management",
  WORK_SCHEDULING: "Work scheduling",
  WORK_SCHEDULING_2: "Work Scheduling",
  COSTING: "Costing",
  STORAGE: "Storage",
  STORAGE_LOCATION_STOCKS: "Storage location stocks",
  FORCASTING: "Forcasting",
  DESCRIPTION: "Description",
  SALES_PLANT: "Sales-Plant",
  SALES_GENERAL: "Sales-General",
  PURCHASING_GENERAL: "Purchasing-General",
  ADDITIONAL_DATA: "AdditionalData",
  ADDITIONAL_EAN_DATA: "AdditionalEANData",
  WAREHOUSE: "Warehouse",
  WORKSCHEDULING: "Work Scheduling",
  CHANGE_LOG_WORK_SCHEDULING: "WorkScheduling",
  TAXCLASSIFICATION: "TaxClassification",
  TAX_DATA: "TaxData",
  FINANCE_COST_DATA: "FinanceCostData",
  BOM:'BOM',
  SOURCE_LIST: 'Source List',
  PIR:'PIR',
  STORAGE: 'Plant Data|Storage',
  STORAGE_PLANT : 'Plant Data|Storage-Plant',
  STORAGE_GENERAL : 'Plant Data|Storage General',
};

export const DIALOUGE_BOX_MESSAGES = {
  LEAVE_PAGE_MESSAGE:
    "You have unsaved changes. Are you sure you want to leave?",
  DRAFT_MESSAGE: "Are you sure! You really want to save as draft?",
  DELETE_MESSAGE: "Are you sure! You really want to delete?",
  CANCEL_MESSAGE: "Are you sure! You really want to cancel?",
  ERROR_MSG: "Failed to fetch data",
  MATL_ERROR_MSG: "Material must be selected from the available options",
  LANG_ERROR_MSG: "The combination of Material and Language must be unique",
  ALTUNIT_ERROR_MSG:
    "The combination of Material and Alternative Unit of Measure must be unique",
  UPLOAD_FAILED: "Attachment Upload Failed. Please Try Again",
  ALT_UPLOAD_ERROR:
    "Oops! Sorry We Ran In Some Issue, Please Try After Sometime",
  MAX_CANCEL_LIMIT: "Maximum 10 rows can be cancelled at once",
  CANCEL_SUCCESS: "Requests Cancelled Successfully",
  CANCEL_FAILED: "Failed Cancelling Requests",
  FIN_TASK_PREFIX: "Finance Task created with Request Id FCA",
  SAVE_AS_DRAFT_MSG:
    "The following materials will be Saved as Draft for the Request !",
  VALIDATE_MSG:
    "The following materials will be processed further in the Request !",
};

export const VISIBILITY_TYPE = {
  MANDATORY: "Mandatory",
  OPTIONAL: "Optional",
  DISPLAY: "Display",
  REQUIRED: "Required",
  ENABLED:"Enabled",
  HIDDEN:'Hidden',
  HEADER:'Header',
  HIDDEN1: 'Hidden1'
};

export const FIELD_VISIBILITY = {
  MANDATORY: "MANDATORY",
};

export const REQUEST_TYPE = {
  CREATE: "Create",
  CHANGE: "Change",
  EXTEND: "Extend",
  CREATE_WITH_UPLOAD: "Create with Upload",
  CHANGE_WITH_UPLOAD: "Change with Upload",
  EXTEND_WITH_UPLOAD: "Extend with Upload",
  FINANCE_COSTING: "Finance Costing",
};

export const prefixMap = {
  Create: "NMA",
  Change: "CMA",
  Extend: "EMA",
  "Create with Upload": "NME",
  "Change with Upload": "CME",
  "Extend with Upload": "EME",
};

export const MODULE_MAP = {
  MAT: "Material",
  PCG: "Profit Center Group",
  CCG: "Cost Center Group",
  CEG: "General Ledger Group",
  CC: "Cost Center",
  PC: "Profit Center",
  GL: "General Ledger",
  IO: "Internal Order",
  BOM: "Bill Of Material",
};

export const DROP_DOWN_SELECT_OR_MAP = {
  [MODULE_MAP.PC]: (state) => state.profitCenterDropdownData?.dropDown || {},
  [MODULE_MAP.PCG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CCG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CC]: (state) => state.costCenterDropDownData?.dropDown || {},
  [MODULE_MAP.PC]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.GL]: (state) => state.generalLedgerDropDownData?.dropDown || {},
  [MODULE_MAP.IO]: (state) => state.internalOrder?.dropDownDataIO || {},

  [MODULE_MAP.BOM]: (state) => state.bom?.dropDownData || {},
};

export const INITIAL_PAYLOAD_MAP = {
  [MODULE_MAP.MAT] : (state) => state.payload.payloadData?.data || state.payload.payloadData || {},
  [MODULE_MAP.PC]: (state) => state.profitCenterDropdownData?.dropDown || {},
  [MODULE_MAP.PCG]: (state) => state.hierarchyData?.requestHeaderData || {},
  [MODULE_MAP.CCG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CC]: (state) => state.costCenterDropDownData?.dropDown || {},
  [MODULE_MAP.PC]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.GL]: (state) => state.generalLedgerDropDownData?.dropDown || {},
  [MODULE_MAP.BOM]: (state) => state.bom?.dropDownData || {},
};

export const VALIDATION_STATUS = {
  default: "default",
};

export const BUTTON_NAME = {
  SAVE: "save",
  SAVE_AS_DRAFT: "Save As Draft",
  VALIDATE: "Validate",
  SEND_BACK:"Send Back",
  CORRECTION:"Correction",
  SUBMIT:'Submit',
  SAP_SYNDICATE: "SAP Syndication",
};

export const REQUEST_STATUS = {
  DRAFT: "Draft",
  DRAFT_IN_CAPS : "DRAFT",
  PENDING_DATA_ENTRY: "Data Entry Pending",
  REJECTED: "Rejected",
  APPROVAL_PENDING: "Approval Pending",
  APPROVED: "Approved",
  CANCELED: "Canceled",
  SENT_BACK_BY_INTERMEDIATE_USER: "Sent Back By Intermediate User",
  SENT_BACK_BY_MDM_TEAM: "Sent Back By MDM Team",
  SUBMITTED_FOR_REVIEW: "Review Pending",
  SYNDICATED_IN_SAP: "Syndicated In SAP",
  SYNDICATED_IN_SAP_DIRECT: "Syndicated In SAP(Direct)",
  SYNDICATION_FAILED: "Syndication Failed",
  SYNDICATION_FAILED_DIRECT: "Syndication Failed(Direct)",
  SYNDICATED_PARTIALLY: "Syndicated Partially",
  SYNDICATED_PARTIALLY_DIRECT: "Syndicated Partially(Direct)",
  SYNDICATED_PARTIALLY_DIRECT_HYPHEN: "Syndicated-Partially(Direct)",
  UPLOAD_FAILED: "Upload Failed",
  UPLOAD_SUCCESSFUL: "Upload Successful",
  VALIDATED_MDM: "Validated-MDM",
  VALIDATED_MDM_DIRECT: "Validated-MDM(Direct)",
  VALIDATION_FAILED_MDM: "Validation Failed-MDM",
  APPROVER_SLA_EXCEEDED: "Approver SLA Exceeded",
  DATA_OWNER_SLA_EXCEEDED: "Data Owner SLA Exceeded",
  MDM_STEWARD_SLA_EXCEEDED: "MDM Steward SLA Exceeded",
  CORRECTION_PENDING: "Correction Pending",
  VALIDATED_REQUESTOR: "Validated-Requestor",
  VALIDATION_FAILED_REQUESTOR: "Validation Failed-Requestor",
  ENABLE_FOR_FIRST_TIME: "Enable For First Time",
  VALIDATION_FAILED_MDM_DIRECT: "Validation Failed-MDM(Direct)",
  SCHEDULED_FOR_SYNDICATION : "Scheduled For Syndication",
  CANCELLED : "Canceled",
  COMPLETED : "Completed",
  IN_PROGRESS : "In Progress",
};

export const ENABLE_STATUSES = [
  REQUEST_STATUS.DRAFT,
  REQUEST_STATUS.VALIDATED_REQUESTOR,
  REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR,
  REQUEST_STATUS.UPLOAD_FAILED,
  REQUEST_STATUS.UPLOAD_SUCCESSFUL,
 REQUEST_STATUS.SYNDICATION_FAILED_DIRECT, REQUEST_STATUS.SYNDICATED_PARTIALLY_DIRECT_HYPHEN, REQUEST_STATUS.ENABLE_FOR_FIRST_TIME,
,REQUEST_STATUS.VALIDATION_FAILED_MDM_DIRECT,REQUEST_STATUS.VALIDATED_MDM_DIRECT];
export const DISABLE_STATUSES = [
  REQUEST_STATUS.SYNDICATED_IN_SAP,
  REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT,
];
export const CHANGE_LOG_STATUSES = [
  REQUEST_STATUS.DRAFT,
  REQUEST_STATUS.VALIDATED_REQUESTOR,
  REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR,
  REQUEST_STATUS.UPLOAD_FAILED,
];

export const CHANGE_KEYS = {
  MATERIAL_TYPE: "Material Type",
  MATERIAL_NUM: "Material Number",
  DIVISION: "Division",
  PLANT: "Plant",
  MRP_CTRLER: "MRP Controller",
  WAREHOUSE: "Warehouse",
  SALES_ORG: "Sales Org",
  DIST_CHNL: "Distribution Channel",
  STORAGE_LOC: "Storage Location",
  CTRL_AREA_PCG: "Controlling Area",
  PRCTR_GRP: "Profit Center Group",
  PRCTR_GRP_DESC: "Profit Center Group Description",
  CCCTR_GRP: "Cost Center Group",
  CCCTR_GRP_DESC: "Cost Center Group Description",
};

export const MODULE_KEY_MAP = {
  PCG: {
    CTR_GRP: CHANGE_KEYS?.PRCTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.PRCTR_GRP_DESC,
  },
  CCG: {
    CTR_GRP: CHANGE_KEYS?.CCCTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.CCCTR_GRP_DESC,
  },
  CEG: {
    CTR_GRP: CHANGE_KEYS?.CECTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.CECTR_GRP_DESC,
  },
};

export const DELETE_MODAL_BUTTONS_NAME = {
  DELETE: "Yes",
  CANCEL: "Cancel",
};
export const DESTINATION = {
  DEST_IDM: "cw-oauth2-idm",
};

export const DESTINATION_FIN = {
  PC: "destination_ProfitCenter_Mass",
  CC: "destination_CostCenter_Mass",
};

export const DECISION_TABLE_NAME = {
  MDG_MAT_REGION_DIVISION_MAPPING: "MDG_MAT_REGION_DIVISION_MAPPING",
  MDG_MAT_IAS_GROUP_ROLE_MAPPING: "MDG_MAT_IAS_GROUP_ROLE_MAPPING",
  MDG_MAT_SEARCHSCREEN_COLUMN:'MDG_MAT_SEARCHSCREEN_COLUMN',
  MDG_MAT_SEARCHSCREEN_PARAMETER:"MDG_MAT_SEARCHSCREEN_PARAMETER",
  MDG_MAT_MATERIAL_FIELD_CONFIG:"MDG_MAT_MATERIAL_FIELD_CONFIG",
  MDG_MAT_REQUEST_HEADER_CONFIG:"MDG_MAT_REQUEST_HEADER_CONFIG",
  MDG_MAT_BOM_CONFIG:"MDG_BOM_MATERIAL_FIELD_CONFIG",
  MDG_MAT_BOM_BUTTONS:"MDG_MAT_DYN_BUTTON_CONFIG",
  MDG_MAT_ATTACHMENT_CONFIG:"MDG_MAT_ATTACHMENT_CONFIG"
};

export const COLUMN_FIELD_TYPES = {
  MULTIPLE:'Multiple',
  DATE:'Date',
  SINGLE:'Single',
  STATUS:'Status',
  DOCUMENTTYPE:'DocumentType'
}

export const SEARCH_FIELD_TYPES = {
  INPUT:'Input',
  CALENDAR:'Calendar',
  REQUESTTYPE:'requestType',
  REQUESTPRIORITY:'requestPriority',
  CREATEDBYUSER:'createdByUser',
  REQSTATUS:'reqStatus',
  DIVISION:'division',
  MATERIALNOS:'materialNos',
  TEMPLATENAME:'templateName',
  DOCTYPE:'docType',
  ATTACHMENTTYPE:'attachmentType',
  UPLOADEDBY:'uploadedBy',
  UPLOADEDDATE:'uploadedDate',
  
  CREATEDON:'createdOn',
  PURSTATUS:'PurStatus',
  DISTRIBUTIONCHANNEL:'distributionChannel',
  MATERIALGROUP:'materialGroup',
  MATERIALTYPE:'materialType',
  NUMBER:'number',
  PLANT:'plant',
  SALESORG:'salesOrg',
  REGION:'Region'
}
export const DT_KEY_NAMES = {
  MDG_MAT_DIVISION: "MDG_MAT_DIVISION",
  MDG_MAT_DIVISION_DESC: "MDG_MAT_DIVISION_DESC",
};

export const LOADING_MESSAGE = {
  DT_LOADING: "Loading DT...",
  REPORT_LOADING:
    "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.",
  CHANGELOG_LOADING: "Loading Changelog...",
  TAXDATA_LOADING: "Loading Tax Data...",
  LOADING_PERM: "Loading permissions...",
  LOADING_USER_ACCESS: "Loading User Access Data, Please Wait...",
};

export const REGION = [
  { code: "US", desc: "US" },
  { code: "EUR", desc: "EUR" },
];

export const REQUEST_PRIORITY = [
  { code: "High", desc: "" },
  { code: "Medium", desc: "" },
  { code: "Low", desc: "" },
];

export const REQUEST_TYPE_OPTIONS = [
    { code: "Create", desc: "Create New BOM in Application" },
    { code: "Change", desc: "Modify Existing BOM in Application" },
    { code: "Create with Upload", desc: "Create New BOM with Excel Upload" },
    { code: "Change with Upload", desc: "Modify Existing BOM with Excel Upload" },
  ]

export const MATERIAL_TYPE_DRODOWN = [
  { code: "FERT", desc: "Finished Goods" },
  { code: "HALB", desc: "Semifinished Goods" },
  { code: "ROH", desc: "Raw Materials" },
  { code: "ZROH", desc: "Spares,Mktg Non-Value" },
];

export const REGION_CODE = {
  US: "US",
  EUR: "EUR",
};

export const DIVERSION_CONTROL_FLAG = [
  { code: "", desc: "No Batch Control" },
  { code: "B", desc: "Perishable Items" },
  { code: "U", desc: "FDA UDI/Batch Controlled" },
  { code: "X", desc: "FDA Batch Control" },
];

export const PRICE_CTRL_DATA = [
  { code: "S", desc: "Standard Price" },
  { code: "V", desc: "Moving Average Price" },
];

export const PAGESIZE = {
  TOP_SKIP: 100,
};
export const DT_TABLES = {
  SALES_DIV_PRICE_MAPPING: "MDG_MAT_SALESDIV_PRICEGRP_MAPPING",
  REG_PLNT_INSPSTK_MAPPING: "MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING",
  MDG_MAT_PRODUCT_HIERARCHY: "MDG_MAT_PRODUCT_HIERARCHY",
  MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT: "MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT",
};
export const ERROR_MESSAGES = {
  NO_DATA_AVAILABLE: "No Data Available",
  CHANGE_LOG_MESSAGE: "Error processing changelog data:",
  NO_FIELDS_CONFIGURED:"No Fields Configured",
  NO_DATA_CONFIGURED:"No Fields Configured for this view",
  NO_RECORDS: "There are no records to display at the moment",
  DATE_VALIDATION_ERROR: "Date validation error:",
  DUPLICATE_COMBINATION:"Duplicate combination found",
  DOCUMENT_DELETE_FAILED: (name) => `Failed to delete ${name}`,
  MANDATORY_FILTER_MD: (name) => `Please fill ${name} first`,
  MANDATORY_FILTER_BOM:"Please fill all fields (Plant, BOM Usage, Alternative BOM) to validate.",
  FETCH_CHANGELOG_ERROR: "Error fetching changelog data:",
  DESCRIPTION_VALIDITY_US:
    "Invalid characters used. Only English alphabets, numbers, '/', '\"', '-', and spaces are allowed",
  DESCRIPTION_VALIDITY_EUR:
    "Invalid characters used. Only English alphabets are allowed. No special characters",
  ERROR_FETCHING_DATA: "Error fetching data",
  ERROR_GET_DISPLAY_DATA: "Error in getDisplaydata processing",
  ERROR_SET_ROLE: "Failed to set role",
  // Dashboard related error messages
  DASHBOARD_REFRESH_FAILED: "Dashboard refresh failed:",
  DECISION_TABLE_FETCH_ERROR: "Decision table fetch error:",
  USER_PREFERENCES_FETCH_ERROR: "User preferences fetch error:",
  GRAPH_DATA_FETCH_ERROR: "Error fetching graph data:",
  DASHBOARD_INIT_FAILED: "Dashboard initialization failed:",
  FILTER_CHANGE_UPDATE_FAILED: "Filter change update failed:",
  // ... other existing error messages
  DATA_NOT_FOUND_FOR_SEARCH: "No data found for the selected criteria.",
  DUPLICATE_MATERIAL: "Duplicate material number ",
  DUPLICATE_MATERIAL_DESCRIPTION: "Duplicate material description ",
  ERROR_NO_USER: "No users found",
  ERROR_PERM: "No permissions found",
  ERROR_SEARCH: "Try adjusting your search criteria",
  ERROR_FETCHING_ROLES: "Error fetching roles & Access",
  ERROR_FETCHING_USER: "Error fetching User List",
  USER_ACCESS_ERROR: "An unknown error occurred, Please try again later.",
  SYSTEM_GENERATED_MSG: [
    "The download process may take some time depending on the file size. Please wait while we prepare your file.",
    "After the download is complete, you can upload the data by navigating to the respective Request ID from the Request Bench.",
  ],
  EMAIL_DELIVERY_MSG: [
    "Email delivery may take a few minutes depending on server load. Please check your spam/junk folder if not received.",
    "Once you receive the email, you can upload the data by navigating to the respective Request ID from the Request Bench.",
  ],
  ERROR_UOM: "Base UOM Code not found in dtData",
  ERROR_EXPORT: "No data available for export.",
  ERROR_FETCHING_LANGU: "Error fetching languages",
  NO_MATERIAL_FOUND: "No material found for the given search parameters",
  WENT_WRONG: "Oops! Something went wrong",
  UNEXPECTED_ERROR:
    "We encountered an unexpected error. Please try reloading the page.",
  NO_DATA_FOUND:
    "No data was found for the selected criteria. Please try again.",
  ERR_DOWNLOADING_EXCEL:
    "Oops! Something went wrong while downloading the excel file. Please try again later.",
  NUMBER_OF_FILES_LIMIT: "You can only upload a maximum of 5 files at once",
  INVALID_MAN_ID: "Invalid Manufacturer ID",
  ERR_MAN_ID: "Error validating Manufacturer ID",
  MAIN_DESCR_MANDATORY: "Main Description is mandatory",
  CANCELED_ERR: "Canceled Requests cannot be fetched !!",
  NO_ERROR_FOUND: "No errors found for this request",
  FAILED_TO_CHECK_INTERNET_SPEED: "Failed to check internet speed (0 Mbps)",
  NO_INTERNET_CONNECTION: "You are offline. Check your internet connection",
  BACK_ONLINE: "You are back online",
  FAILED_FETCH_SAP_SYSTEM: "Failed to fetch SAP system configuration",
  DOC_UPLOAD_FILES_LIMIT: "Cannot upload more than 5 files",
  DOC_UPLOAD_SIZE_LIMIT: "File size exceeded 50MB",
  ERROR_REQUEST_HEADER: "Error occured while saving Request Header",
  FAILED_UPDATE_SAP_SYSTEM: (system) => `Failed to update SAP system to ${system}`,
};
export const ROLES = {
  SUPER_USER: "CA-MDG-SUPER-USER",
};

export const FIELD_TYPE = {
  INPUT: "Input",
  DROPDOWN: "Dropdown",
  DATE_FIELD: "Date field",
};

export const PAGE_TYPE = {
  DISPLAY: "display",
  REQUESTOR: "requestor",
};

export const EXPORT_EXCEL_KEYS = {
  REGION: "Region",
  MATERIAL_TYPE: "Material Type",
  MATERIAL_NUMBER: "Material Number",
  SALES_ORG: "Sales Org",
  DISTRIBUTION_CHANNEL: "Distribution Channel",
  PLANT: "Plant",
  WAREHOUSE: "Warehouse",
  STORAGE_LOCATION: "Storage Location",
};

export const EXPORT_PARAMETERS = [
  {
    key: EXPORT_EXCEL_KEYS.REGION,
    options: REGION,
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.MATERIAL_TYPE,
    options: MATERIAL_TYPE_DRODOWN,
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.MATERIAL_NUMBER,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.SALES_ORG,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.PLANT,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.WAREHOUSE,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.STORAGE_LOCATION,
    options: [],
    singleSelect: true,
  },
];

export const SUCCESS_MESSAGES = {
  DOCUMENT_DELETED: (name) => `${name} has been deleted successfully`,
  DUPLICATE_COMBINATION:"Validation successful - No duplicate combination found.",
  REQUEST_HEADER_CREATED:"Request Header Created Successfully with request ID",
  DB_OPERATIONAL: "Database is operational",
  EMAIL_OPERATIONAL: "Email service is working fine",
  IDM_OPERATIONAL: "IDM Service is active",
  ODATA_OPERATIONAL: "OData Service is operational",
  BPA_OPERATIONAL: "SAP BPA Workflow Service is operational",
  SHAREPOINT_OPERATIONAL: "SharePoint Service is operational",
  USER_FETCHED: "Users fetched successfully",
  USER_ACCESS_SUCCESS: "User Access Data Fetched Successfully",
  DOWNLOAD_MAIL_INITIATED:
    "Download has been started. You will get the Excel file via email.",
  MAN_ID_VALID: "Manufacturer ID Validated Successfully",
  SAP_DOWNLOAD_SUCCESS:
    "SAP Excel Report.xlsx has been downloaded successfully.",
  SUCCESS_REQUEST_HEADER: "Request Header Created Successfully! Request ID: "
};

export const INFO_MESSAGES = {
  FETCHING_REQUEST_ID:'Please wait while we retrieve the Request ID.',
  FETCHING_REQUEST_TYPE:'Please wait while we retrieve the Request Type.',
  TEMPLATE_MESSAGE:'Choose a template category from the sidebar to view its templates',
   FETCH_ROLE:'Please wait while we retrieve the Role',
}

export const ERROR_MESSAGES_HEALTH = {
  DB_DOWN: "Database service is down",
  EMAIL_DOWN: "Email service is facing issues",
  IDM_DOWN: "IDM Service is down",
  ODATA_DOWN: "OData Service is facing issues",
  BPA_DOWN: "SAP BPA Workflow Service is facing issues",
  SHAREPOINT_DOWN: "SharePoint Service is facing issues",
  WENT_WRONG: "Oops! Something went wrong",
  UNEXPECTED_ERROR:
    "We encountered an unexpected error. Please try reloading the page",
};

export const DROP_DOWN_ITEM_SIZE = {
  HEIGHT: 32,
};
export const CHARACTER_LIMIT = {
  EUR: "35",
  US: "26",
  US_SPECIAL: "12",
};
export const DASHBOARD_REPORT_LABELS = {
  ALL_MATERIALS_BY_PLANT_AND_SALES:
    "List of materials created by plant & Sales Org (update of interco price)",
  DRAFT_STATUS: "List of Request Ids in draft status",
  MATERIAL_LIST: "Created materials list",
  PRICING_GROUP: "Material Pricing group",
  MISSING_HTS: "Missing HTS Code Report",
  REQUEST_VS_OBJECT_TYPE:"Request (In Nos) Vs Object Type",
  AVG_REQUEST_ID_LIFE_CYCLE:"Avg Request Id Life Cycle (In Hours) Vs Object Type",
  REJECTED_REQUESTS_PERCENTAGE:"Not Successfully Completed Req Ids due to Rejection(in %age) Vs Object Type",
  NOT_SUCCESSFULLY_COMPLETED_REQUESTS:"Not Successfully Completed Req Ids (in %age) Vs Object Type",
  SLA_BREACHED_REQUESTS:"SLA Breached Req Ids(In Nos) Vs Object Type",
  NO_OF_REQUESTS_REJECTED:"No. of Requests Rejected/Cancelled(In Nos) Vs Users",
  NO_OF_REQUESTS_PENDING:"No. of Requests Pending(In Nos) Vs Approvers/MDM",
  AVG_MDM_TASK_COMPLETION_TIME:"Avg time taken by MDM Task For Completion Vs Object Type",
  AVG_APPROVER_TASK_COMPLETION_TIME:"Avg time taken by Approver Task Vs Object Type",
  NO_OF_REQUESTS_REJECTED_VS_REQUESTORS:"Req Ids Rejected (in Nos) Vs Requestors",
  APPROVERS_WHO_HAVE_MISSED_SLA:"Approvers Who have Missed SLA(In Nos)",
  APPROVERS_AVG_APPROVAL_TIME:"Approvers Avg. Approval Time(In Hours)",
  REQUESTS_COMPLETED_BY_MDM:"No of Requests Completed By MDM Team",
  OPEN_REQUESTS_VS_TYPE:"Open Requests(In Nos) Vs Object Type",
  REQUESTOR_APPROVER_COUNTS:"Requestor/Approvers Counts Vs Object Type",
  OPEN_WORKFLOWS_REPORT: "Open Workflows Report",
  TEMP_BLOCK_REQUESTS_REPORT: "Temporary Block Requests Report",
  SCHEDULED_REQUESTS_REPORT: "Scheduled Requests Report",
  PDF_SCHEDULER_REPORT: "PDF Scheduler Report",
  ON_HOLD_WITH_REQUESTORS_REPORT: "Requests On Hold With Requestors",
  ALL_ERROR_REQUESTS_REPORT: "All Error Requests",
  OBJECT_NUMBERS_REPORT: "Object Numbers Report",
  SYNDICATION_ATTACHMENT_FAILED_REPORT: "Syndication & Attachement Failed Report"
};

export const SEARCH_BAR_LABELS = {
  TOOLTIP: "Search for Table data",
  LABEL: "Search Documents",
};

export const TASK_NAME = {
  REQ_INITIATE: "Z_MAT_REQ_INITIATE",
  REQ_INITIATE_FIN: "Z_FIN_REQ_INITIATE",
  REQUESTOR: "Requestor",
  INITIATOR:'Initiator'
};

export const REGEX = {
  ADDING_SPACE: /([A-Z])/g,
};

export const DASHBOARD_CARD_TITLE = {
  REQUEST_VS_OBJECT_TYPE: "Request (In Numbers) Vs Request Type",
  PENDING_REQUESTS_VS_GROUPS:
    "Number of Requests Pending (In Numbers) Vs Groups",
  AVG_MDM_TASK_COMPLETION_TIME:
    "Avg time taken by MDM Task For Completion Vs Request Type",
  REJECTED_REQUESTS_PERCENTAGE:
    "Not Successfully Completed Req Ids due to Rejection(in %age) Vs Request Type",
  OPEN_REQUESTS_VS_TYPE: "Open Requests In Workspace Vs Request Type",
  REQUESTS_COMPLETED_BY_MDM: "Number of Requests Completed By MDM Users",
  APPROVERS_AVG_APPROVAL_TIME: "Approvers Avg. Approval Time(In Hours)",
  REQUESTS_VS_CHANGE_TEMPLATE: "Number of Requests Vs Change Template",
  REQUESTS_VS_REGION: "Number of Requests Vs Region",
  TOP_5_REQUESTORS: "Top 5 Requestors",
};

export const DT_FIELDS_NAME = {
  COUNTRY_OF_ORIGIN: "Countryori",
  VAL_CLASS: "ValClass",
  PARENT_MAT_NUMBER: "ParentMatNumber",
  RETURN_MAT_NUMBER: "ReturnMatNumber",
};
export const DEFAULT_VALUES = {
  DEFAULT_IND_SECTOR: { code: "M", desc: "Mechanical engineering" },
  DEFAULT_TAX_CLASS: { TaxClass: "1", TaxClassDesc: "Taxable" },
};

export const HEADINGS = {
  COPY_ORG_DATA_VALUES: "Choose Sales Combination to copy",
  COPY_ORG_DATA_VALES_HEADING: "Select Org Data to Copy",
};
export const LOADER_MESSAGES = {
  VALIDATING_MATS: "Validating Materials, please wait!",
  VALIDATE_MANDATORY: "Validate mandatory fields before proceeding",
};

export const SERVICE_NAMES = {
  DATABASE: "DATABASE",
  IDM: "IDM",
  MAIL: "MAIL",
  SAP_BPA_WORKFLOW: "SAP_BPA_WORKFLOW",
  ODATA: "ODATA",
  SHAREPOINT: "SHAREPOINT",
};

export const SERVICE_NAME_MAP = {
  [SERVICE_NAMES.DATABASE]: "Database Services",
  [SERVICE_NAMES.IDM]: "IDM Services",
  [SERVICE_NAMES.MAIL]: "Email Services",
  [SERVICE_NAMES.SAP_BPA_WORKFLOW]: "SAP BPA Workflow Services",
  [SERVICE_NAMES.ODATA]: "OData Services",
  [SERVICE_NAMES.SHAREPOINT]: "SharePoint Services",
};

export const RELATION_DROPDOWN = [
  { code: "Parent", desc: "Parent" },
  { code: "Child", desc: "Child" },
];
export const CATEGORY_DROPDOWN = [{ code: "L", desc: "Stock Item" }];

export const CHAT_MESSAGES = {
  CREATE_ACC: "Create an account, login, and select a user from the sidebar to start your conversation",
  WELCOME:"Welcome to Chat",
  MESSAGE:"Type your message...",
  ACTIVE: "Active now",
  CREATE_LOGIN:"Create an account and login to see other users",
  NO_USERS: "No Users Found",
  NO_USER:"User not found",
  WEBSOCKET_DISCONNECTED:"WebSocket not connected. Please refresh and try again.",
  ERROR:"Error loading messages:",
  ERROR_LOGGING:"Error logging in: ",
  EMAIL:"Please enter an email address",
};

export const EXCLUDED_VIEWS = [
  "Header",
  "Sales-General",
  "Sales-Plant",
  "Purchasing-General",
  "Warehouse Management",
  "Forecasting",
  "Storage Location Stocks",
  "Plant Stock",
  "Quality Management",
  "productionResources/tools",,
  "BOM",
  "Source List",
  "PIR",
  "Plant Data|Storage General",
  "Plant Data|Storage-Plant",
];
export const UI_HIDDEN_VIEWS = [
  MATERIAL_VIEWS.SALES_PLANT,
  MATERIAL_VIEWS.STORAGE_PLANT,
];

export const ALT_UNITS = {
  EA: "EA",
  CA: "CA",
  CT: "CT",
  PAL: "PAL",
  PA: "PA",
};

export const EAN_CATEGORIES = {
  IE: "IE",
  IC: "IC",
  UC: "UC",
  MB: "MB",
  MI: "MI",
};

export const TABLE_FIELDS_UOM = {
  EAN_UPC: "eanUpc",
  EAN_CATEGORY: "eanCategory",
  MAIN_EAN: "MainEan",
};

export const LOCAL_STORAGE_KEYS = {
  CURRENT_TASK: "currentTask",
  REQUEST_BENCH_TASK: "requestBenchTask",
  ROLE: "role",
  MODULE:'module'
};

export const FIELD_NAME_MAPPINGS = {
  plant: "Plant",
  salesOrg: "Sales Organization",
  group: "Material Group",
  number: "Material Number",
  description: "Material Description",
  oldMaterialNumber: "Old Material Number",
  labOffice: "Lab/Office",
  transportationGroup: "Transportation Group",
  productHierarchy: "Product Hierarchy",
  changedBy: "Changed By",
  changedOn: "Changed On",
  basicMaterial: "Basic Material",
  division: "Division",
  createdBy: "Created By",
  distributionChannel: "Distribution Channel",
  storageLocation: "Storage Location",
  warehouseNo: "Warehouse No",
  PurStatus: "X-Plant Material Status",
  type: "Material Type",
  createdOn: "Created On",
  XplantMaterialStatus: "X-Plant Material Status",
  purchasingGroup: "Purchasing Group",
};

export const MODULE = {
  PCG: "PCG",
  CCG: "CCG",
  CC: "CostCenter",
  GL: "GeneralLedger",
  BOM: "BOM",
  IO: "InternalOrder",
};

export const DESCRIPTION_DATA = {
  CATEGORY: "Description",
  TABLE_HEADERS: {
    ID: "ID",
    LANGUAGE: "Language",
    DESCRIPTION: "Material Description"
  },
  FIELDS: {
    ID: "id",
    LANGUAGE: "language",
    DESCRIPTION: "materialDescription"
  },
  ACCORDION_TITLE: "Material Descriptions"
};

export const UOM_DATA = {
  CATEGORY: "Units of Measure",
  TABLE_HEADERS: {
    ALT_UNIT: "Alternative Unit",
    NUMERATOR: "Numerator",
    DENOMINATOR: "Denominator",
    EAN_UPC: "EAN/UPC",
    EAN_CATEGORY: "EAN Category",
    DIMENSIONS: "Dimensions (L×W×H)",
    DIMENSION_UNIT: "Dimension Unit",
    VOLUME: "Volume",
    VOLUME_UNIT: "Volume Unit",
    GROSS_WEIGHT: "Gross Weight",
    NET_WEIGHT: "Net Weight",
    WEIGHT_UNIT: "Weight Unit"
  },
  FIELDS: {
    ALT_UNIT: "aUnit",
    NUMERATOR: "yValue",
    DENOMINATOR: "xValue",
    EAN_UPC: "eanUpc",
    EAN_CATEGORY: "eanCategory",
    LENGTH: "length",
    WIDTH: "width",
    HEIGHT: "height",
    DIMENSION_UNIT: "unitsOfDimension",
    VOLUME: "volume",
    VOLUME_UNIT: "volumeUnit",
    GROSS_WEIGHT: "grossWeight",
    NET_WEIGHT: "netWeight",
    WEIGHT_UNIT: "weightUnit"
  },
  ACCORDION_TITLE: "Units of Measure"
};

export const TAX_DATA = {
  CATEGORY: "TaxData",
  SUB_CATEGORY: "TaxData",
  DATASET: "TaxDataSet",
  TABLE_HEADERS: {
    COUNTRY: "Country",
    TAX_TYPE: "Tax Type",
    TAX_CLASS: "Tax Class",
    DESCRIPTION: "Description"
  },
  FIELDS: {
    COUNTRY: "Country",
    TAX_TYPE: "TaxType",
    TAX_CLASS: "TaxClass",
    TAX_CLASS_DESC: "TaxClassDesc",
    SELECTED_TAX_CLASS: "SelectedTaxClass"
  },
  ACCORDION_TITLE: "Tax Classification"
};
export const WORK_FLOW_LEVELS = {
  '-1':'Requestor',
  0:'Final Creation',
  1:'Data Entry',
  2:'Additional Master Data',
  3:'Cost',
  4:'Record Approval'
}
export const DASHBOARD_COLUMNS = {
  FIRST:'First',
  SECOND:'Second',
  THIRD:'Third'
}
export const DASHBOARD_MAIN_COLUMNS = [
  {
    id:DASHBOARD_COLUMNS.FIRST
  },
  {
    id:DASHBOARD_COLUMNS.SECOND
  },
  {
    id:DASHBOARD_COLUMNS.THIRD
  }
]
export const CHART_TYPE = {
  BAR: 'BAR',
  STACKED_BAR: 'STACKED_BAR',
  LINE: 'LINE',
  STACKED_LINE: 'STACKED_LINE',
  STACK_LINE: 'STACK_LINE',
  COLUMN: 'COLUMN',
  STACK_COLUMN: 'STACK_COLUMN',
  PIE: 'PIE',
  AREA: 'AREA',
  STACKED_AREA: 'STACKED_AREA',
  DONUT: 'DONUT'
};

export const LINEAR_CHART_TYPES = [
  { value: CHART_TYPE.BAR, label: 'Bar' },
  { value: CHART_TYPE.STACKED_BAR, label: 'Stacked Bar' },
  { value: CHART_TYPE.COLUMN, label: 'Column' },
  { value: CHART_TYPE.STACK_COLUMN, label: 'Stacked Column' },
  { value: CHART_TYPE.LINE, label: 'Line' },
  { value: CHART_TYPE.STACKED_LINE, label: 'Stacked Line' },
  { value: CHART_TYPE.STACK_LINE, label: 'Stacked Line' },
  { value: CHART_TYPE.AREA, label: 'Area' },
  { value: CHART_TYPE.STACKED_AREA, label: 'Stacked Area' }
];

export const CIRCULAR_CHART_TYPES = [
  { value: CHART_TYPE.PIE, label: 'Pie' },
  { value: CHART_TYPE.DONUT, label: 'Donut' }
];

export const STACKED_CHART_TYPES = [
  CHART_TYPE.STACK_COLUMN, 
  CHART_TYPE.STACKED_BAR, 
  CHART_TYPE.STACKED_LINE,
  CHART_TYPE.STACK_LINE,
  CHART_TYPE.STACKED_AREA
];

export const HOME_CAROUSEL_BANNER = [
    {
      id: 1,
      title: "Augment your customer experience",
      subtitle: "Transform your business with cutting-edge solutions",
      description: "Leverage advanced analytics and AI-powered insights to deliver exceptional customer experiences that drive growth and satisfaction.",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=1200&h=500&fit=crop",
      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    },
    {
      id: 2,
      title: "Innovate with confidence",
      subtitle: "Next-generation collaboration tools",
      description: "Streamline your workflow with intelligent automation and real-time collaboration features designed for modern businesses.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=500&fit=crop",
      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
    },
    {
      id: 3,
      title: "Scale your success",
      subtitle: "Enterprise-grade performance",
      description: "Built for scale with enterprise security, 99.9% uptime, and seamless integration with your existing systems.",
      image: "https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=1200&h=500&fit=crop",
      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
    }
  ]
export const VAR_ORD_UNIT = [
  {code: '1', desc:'Active'},
  {code: '2', desc: 'Active with own price'}
]

export const REQUEST_HEADER_FILED_NAMES = {
  REQUEST_TYPE:'RequestType',
  REQUEST_ID:'RequestId'
}

export const DOC_SNACKBAR_MESSAGES = {
  FILES: {
    FETCH_ERROR: 'Error fetching files',
    UPLOAD_SUCCESS: (count) => `${count} file(s) upload has been initiated`,
    UPLOAD_FAILED: 'Upload failed',
    UPLOAD_ERROR: 'Upload error',
    DELETE_SUCCESS: 'File deleted successfully',
    DELETE_FAILED: 'Failed deleting document',
    DELETE_ERROR: 'Failed deleting document',
    VISIBILITY_SUCCESS: (isVisible) => `File ${isVisible ? 'made visible' : 'hidden'} successfully`,
    VISIBILITY_FAILED: 'Visibility update failed',
    VISIBILITY_ERROR: 'Visibility update error',
    MISSING_USER:'Missing userId for edit action'
  }
};
export const DATA_CLEANSE_CONSTANTS={
  MODULE:'Type of Module',
  NO_OBJECTS:'Number of Objects',
  BUSINESS_RULES:'Total Business Rules',
  AGGREGATE_SCORE:'Aggregate Score',
  CREATEDBY:'Created By',
  SEARCH:'Search materials...',
  MATERIAL:'Material',
  NO_MATERIAL_FOUND:'No materials found',
  ADJUST:'Try adjusting your search terms',
  ANALYSIS_DETAILS:'Analysis Details',
  OVERALL_SCORE:'Overall Score',
  BUSINESS_RULE:'Business Rule',
  FIELDS:'Fields',
  PASS:'Pass',
  FAIL:'Fail',
  SCORE:'Score',
  PRESENT_VALUE:'Present Value',
  EXPECTED_VALUE:'Expected Value',
  NO_MATERIAL:'No Material Selected',
  VALIDATIONS_DETAILS:'Select a material from the sidebar to view its validation details',
  CLEANSE_REQUEST:'Data Cleanse Request',
  VIEW:'This view displays the details of the materials data cleansing report',
  PDF:'Download as PDF',
  ERROR:'An error occurred while creating the request',
  DATA_CLEANSE:'Data Cleanse',
  FILTER:'Filter Data Cleanse Requests',
  NEW_REQUEST:'Create New Request',
  INITIATE_REQUEST:'Initiate Request',
  CREATE_REQUEST:'Create Request',
  SELECT_MATERIAL:'Select Material',
  MATERIAL_TYPE:'Material Type',
  SELECT_MATERIAL_TYPE:'Select Material Type',
  MATERIAL_GROUP:'Material Group',
  SELECT_MATERIAL_GROUP:'Select Material Group',
  SELECT_COST_CENTER:'Select Cost Center',
  CATEGORY:'Cost Center Category',
  SELECT:'Select Business Rule',
  FAILED_FETCHING_DATA:'No Data Found for this Data Cleanse Request!',
  EXPORT_SUCCESS: `_Data Cleanse.pdf has been exported successfully!`,
  BR_FAIL: 'Business Rule is required',
  NO_FAIL: 'Number of Objects is required',
  CO_FAIL: 'Created On date range is required',
}

export const LOGOUT = 'LOGOUT'

export const ARTIFACTNAMES = {
  MATERIALMASTER:'MaterialMaster'
}