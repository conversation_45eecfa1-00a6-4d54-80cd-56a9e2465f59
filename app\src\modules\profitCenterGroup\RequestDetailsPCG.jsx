import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import ReusableHierarchyTree from "@components/MasterDataCockpit/Hierarchy/ReusableHIerarchyTree";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { Box, Typography } from "@mui/material";
import { colors } from "@constant/colors";
import { createPayloadForPCG } from "../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import {  MODULE, MODULE_MAP } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import useChangeListPayloadHierarchy from "../modulesHooks/hierarchyHooks/useChangeListPayloadHierarchy"

const RequestDetailsPCG = (props) => {
  const [showTree, setShowTree] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success"); // 'success' or 'error'
  const taskData = useSelector((state) => state.userManagement.taskData);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const RequestId = queryParams.get("RequestId");

  const hierarchyRequestHeaderData = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const { preparePayload } = useChangeListPayloadHierarchy();
  const initialNodeData = useSelector((state) => state.hierarchyData.treeData);
  const requestorPayload = useSelector(
    (state) => state.payload.requestorPayload
  );
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const reduxPayload = useSelector((state) => state.hierarchyData);
  const treeChanges = useSelector((state) => state.hierarchyData.TreeChanges);
  const navigate = useNavigate();

  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  useEffect(() => {
    if (taskData?.ATTRIBUTE_1 || RequestType) {
      getButtonsDisplayGlobal(
        "Hierarchy Node (Profit Center)",
        "MDG_DYN_BTN_DT",
        "v3"
      );
    }
  }, [taskData]);

  useEffect(() => {
    if (initialNodeData?.length !== 0) {
      setShowTree(true);
    }
  }, [initialNodeData]);

  const handleButtonClick = async (type) => {
    let apiEndpoint =
      END_POINTS?.MASTER_BUTTON_APIS?.[MODULE?.PCG]?.[
        hierarchyRequestHeaderData?.RequestType
      ]?.[type];
  
    const result = preparePayload(treeChanges);
    setBlurLoading(true);
   if (!result?.success) {
      return;
    }

  const finalPayload = createPayloadForPCG(
    {
      ...reduxPayload, 
      NodesList:result.NodeList,
      ReplaceNodesList:result.ReplaceNodesList,
      TagList:result.TagList,
      ReplaceTagList:result.ReplaceTagList,
      DescList:result.DescList,
      EditDescList:result.EditDescList,
      DeleteNodeList:result.DeleteNodeList,
      nodesListForDBDuplicateCheck: result.nodesListForDBDuplicateCheck,
      descListForDBDuplicateCheck: result.descListForDBDuplicateCheck,
    },
    requestHeaderSlice,
    RequestId,
    taskData,
    dynamicData,
    requestorPayload,
    type
  );
    const hSuccess = (data) => {
      if (data.statusCode >= 200 && data.statusCode < 300) {
        setBlurLoading(false);
        setAlertType("success");
        setAlertMsg(data?.message || "");
        setOpenSnackBar(true);
        setTimeout(() => {
          navigate(
            END_POINTS?.MASTER_BUTTON_APIS?.[MODULE?.PCG]?.[
              hierarchyRequestHeaderData?.RequestType
            ]?.[type]?.NAVIGATE_TO
          );
        }, 2000);
      } else {
        setBlurLoading(false);
        setAlertType("error");
        setAlertMsg(data?.error || data?.message || "Error Fetching Data !!");
        setOpenSnackBar(true);
      }
    };

    const hError = (error) => {
      console.log('check error', error)
      setBlurLoading(false);
      setAlertType(error || "");
      setAlertMsg(error?.error);
      setOpenSnackBar(true);
    };

    doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
  };

  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  const renderValue = (value) => {
    if (
      Array.isArray(value) &&
      value.length === 1 &&
      typeof value[0] === "object" &&
      value[0].code &&
      value[0].desc
    ) {
      return (
        <>
          <Typography component="span" variant="body1">
            {value[0].code}
          </Typography>
        </>
      );
    }
    return typeof value === "string" ? value : JSON.stringify(value);
  };

  return (
    <div>
      <Box
        sx={{
          backgroundColor: colors.primary.whiteSmoke,
          px: 2,
          py: 1,
          borderBottom: `1px solid ${colors.primary.whiteSmoke}`,
          borderRadius: "5px",
          mb: 2,
        }}
      >
        {requestorPayload && Object?.keys(requestorPayload) && (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 3 }}>
            {Object.keys(requestorPayload).map((key) => (
              <Box
                key={key}
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 700, color: colors.primary.grey }}
                >
                  {key} :
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  {renderValue(requestorPayload[key])}
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Box>
      {showTree && (
        <ReusableHierarchyTree
          initialRawTreeData={initialNodeData}
          editmode={true}
          object={"PCG"}
        />
      )}
      <ReusableBackDrop blurLoading={loadForFetching || blurLoading} />
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        // isLoading={isLoading}
      />
      <BottomNavGlobal
        handleSaveAsDraft={handleButtonClick}
        handleSubmitForReview={handleButtonClick}
        // handleSubmitForApprove={handleSubmitForApprove}
        // handleSendBack={handleSendBack}
        // handleCorrection={handleCorrection}
        // handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleButtonClick}
        // validateAllRows={validateAllRows}
        // isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        // validateEnabled={validateEnabled}
        filteredButtons={filteredButtons}
        moduleName={MODULE_MAP?.PCG}
        isHierarchy = {true}
      />
      <ToastContainer />
    </div>
  );
};

export default RequestDetailsPCG;
