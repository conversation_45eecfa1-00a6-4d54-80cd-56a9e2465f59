import { <PERSON>, <PERSON>ton, Grid, <PERSON><PERSON>, <PERSON>po<PERSON>,  Text<PERSON>ield, Autocomplete, Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import React, { Fragment, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setDropDown } from "./slice/materialDropdownSlice";
import FilterField from "@components/common/ReusableFilterBox/FilterField";
import { container_Padding } from "@components/Common/commonStyles";
import { setRequestHeader, setTabValue } from "@slice/requestDataSlice";
import { ToastContainer } from "react-toastify";
import { doAjax } from "@components/Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { setSelectedSections } from '@slice/selectedSelectionsSlice';
import { newMaterialData, updateAllTabsData, changeTemplateDT } from "@slice/tabsDetailsSlice";
import { useNavigate } from "react-router-dom";
import {setChangeTableData, setMultipleMaterialPayloadKey, setPayload } from "@slice/payloadslice";
import { setUserDetails } from "@slice/userManagementSlice";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { useLocation } from "react-router-dom";
import useMaterialRequestHeaderConfig from "@hooks/useMaterialRequestHeaderConfig";
import useProfitcenterRequestHeaderConfig from "@hooks/useProfitcenterRequestHeaderConfig";
import useMaterialChangeFieldConfig from "@hooks/useMaterialChangeFieldConfig";
import { setTaskData } from "@app/userManagementSlice";
import RequestDetailsForChange from "@material/change/RequestDetailsForChange";
import { TEMPLATE_KEYS } from "@constant/changeTemplates";
import { API_CODE, DECISION_TABLE_NAME, DT_FIELDS_NAME, ERROR_MESSAGES, REQUEST_HEADER_FILED_NAMES, REQUEST_TYPE, SUCCESS_MESSAGES, VISIBILITY_TYPE } from "@constant/enum";
import { appendPrefixByJavaKey, filterConfigData, filterFieldNameData } from "@helper/helper";
import { getRoleForWorkflow } from "../../functions";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import DownloadDialog from "@components/Common/DownloadDialog";
// import useCheckUserAccess from "@hooks/useCheckUserAccess";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import { END_POINTS } from "@constant/apiEndPoints";
import useGenericDtCall from "@hooks/useGenericDtCall";
import useLogger from "@hooks/useLogger";
import { APP_END_POINTS } from "@constant/appEndPoints";
import useLang from "@hooks/useLang";

const RequestHeader = ({ setIsSecondTabEnabled, setIsAttachmentTabEnabled, requestStatus, downloadClicked, setDownloadClicked }) => {
  const [dropDownData, setDropDownData] = useState({});
  const [open, setOpen] = useState(false);
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [formData, setFormData] = useState({});
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [distributionChannelLookupData, setDistributionChannelLookupData] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestHeaderDetails = useSelector((state) => state.tabsData.requestHeaderData);
  const changeData = useSelector((state) => state.tabsData.changeFieldsDT);
  let roles = useSelector((state) => state.userManagement.roles);
  const payloadFields = useSelector((state) => state.payload.payloadData);
  const userData = useSelector((state) => state.userManagement.userData);
  const entitiesActivities = useSelector((state) => state.userManagement?.entitiesAndActivities?.Material);
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const regionBasedSalesOrgData = useSelector(
    (state) => state.request.salesOrgDTData
  );
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");
  const { t } = useLang();

  const { getRequestHeaderTemplate } = useMaterialRequestHeaderConfig();
  const { getChangeTemplate } = useMaterialChangeFieldConfig();
  const {getRequestHeaderTemplatePc} = useProfitcenterRequestHeaderConfig()
  // const { getUserAccessData, isError } = useCheckUserAccess();
  const { fetchOrgData } = useMaterialFieldConfig();
  const { getDtCall } = useGenericDtCall();
  const { customError } = useLogger();

  const requestTypeData = [
    { code: "Create", desc: "Create New Material in Application" },
    { code: "Change", desc: "Modify Existing Material in Application" },
    { code: "Extend", desc: "Extend Existing Material in Application" },
    { code: "Create with Upload", desc: "Create New Material with Excel Upload" },
    { code: "Change with Upload", desc: "Modify Existing Material with Excel Upload" },
    { code: "Extend with Upload", desc: "Extend Existing Material with Excel Upload" },
  ];

  const reqTypeOptionsRoleWise = requestTypeData.filter((item) =>
    entitiesActivities?.includes(item.code)
  );
  const leadingCategory = [
    { code: "Oncology", desc: "" },
    { code: "Anesthesia/Pain Management", desc: "" },
    { code: "Cardiovascular", desc: "" },
  ];
  const templateNames = [
    { code: TEMPLATE_KEYS?.LOGISTIC, desc: "" },
    { code: TEMPLATE_KEYS?.MRP, desc: "" },
    { code: TEMPLATE_KEYS?.WARE_VIEW_2, desc: "" },
    { code: TEMPLATE_KEYS?.ITEM_CAT, desc: "" },
    { code: TEMPLATE_KEYS?.SET_DNU, desc: "" },
    { code: TEMPLATE_KEYS?.UPD_DESC, desc: "" },
    { code: TEMPLATE_KEYS?.CHG_STAT, desc: "" },
  ];
  const requestPriority = [
    { code: "High", desc: "" },
    { code: "Medium", desc: "" },
    { code: "Low", desc: "" },
  ];

  // let filterFields = Object?.entries(requestHeaderDetails);
  dispatch(setDropDown({ keyName: REQUEST_HEADER_FILED_NAMES?.REQUEST_TYPE, data: reqTypeOptionsRoleWise }));
  dispatch(setDropDown({ keyName: "LeadingCat", data: leadingCategory }))
  dispatch(setDropDown({ keyName: "RequestPriority", data: requestPriority }))
  dispatch(setDropDown({ keyName: "TemplateName", data: templateNames }))
  if (!isWorkspace && !isreqBench) {
    dispatch(setMultipleMaterialPayloadKey({ keyName: "ReqCreatedBy", data: userData?.user_id }));
    dispatch(setMultipleMaterialPayloadKey({ keyName: "RequestStatus", data: "DRAFT" }));
  }

  const fixedOption = "Basic Data";
  const [selectedSections, setSelectedSectionsState] = useState([fixedOption]);
  const [filteredViewType, setFilteredViewType] = useState('');
  const [fieldReference, setFieldReference] = useState('');
  const [disableProceed, setDisableProceed] = useState(true);

  useEffect(() => {
    dispatch(setSelectedSections(selectedSections));
  }, [dispatch, selectedSections]);

  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (payloadFields && requestHeaderDetails[Object.keys(requestHeaderDetails)]?.length) {
      requestHeaderDetails[Object.keys(requestHeaderDetails)[0]]?.forEach(reqst => {
        if (!payloadFields[reqst.jsonName] && reqst.visibility === VISIBILITY_TYPE?.MANDATORY) {
          allFilled = false;
        }
      });
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  useEffect(() => {
    if (payloadFields?.MatlType) {
      getViews(payloadFields);
    }
    checkAllFieldsFilled()
  }, [payloadFields]);

  const getViews = (payloadFields) => {
    const hSuccess = (data) => {
      setFilteredViewType(data.body[0].MaintStatus.split(""));
      setFieldReference(data.body[0].MaterialType);
      //getAutocompleteOptions(filteredViewType);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewForMaterialType?materialType=${payloadFields?.MatlType?.code}`,
      "get",
      hSuccess,
      hError
    );
  }
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if(!isWorkspace) {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
    }

  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.");
    setBlurLoading(true);
    let payload = {
      "region": initialPayload?.Region,
      "scenario": initialPayload?.RequestType,
      "matlType": "ALL",
      "dtName": "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      "version": "v1",
      "rolePrefix": "",
      "requestId": requestHeaderData?.requestId ? requestHeaderData?.requestId : initialPayload?.RequestId ? initialPayload?.RequestId : "",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        setSuccessMsg(true);
        setMessageDialogMessage("No data found for the selected criteria.");
        setAlertType("danger");
        handleSnackBarOpen();
        return
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? 'Mass_Extend.xlsx' : 'Mass_Create.xlsx'}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(`${initialPayload?.TemplateName ? `${initialPayload.TemplateName}_Mass Change` : initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? "Mass_Extend" : "Mass_Create"}.xlsx has been downloaded successfully.`);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(`/requestBench`);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
    }

    const downloadUrl = `/${destination_MaterialMgmt}${initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL
      }`;
    doAjax(downloadUrl, "postandgetblob", hSuccess, hError, payload);
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    let payload = {
      "region": initialPayload?.Region,
      "scenario": initialPayload?.RequestType,
      "matlType": "ALL",
      "dtName": "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      "version": "v1",
      "rolePrefix": "",
      "requestId": requestHeaderData?.requestId ? requestHeaderData?.requestId : initialPayload?.RequestId ? initialPayload?.RequestId : "",
    };
    const hSuccess = () => {

      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL);
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }

    const downloadUrl = `/${destination_MaterialMgmt}${initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAIL
      }`;

    doAjax(downloadUrl, "post", hSuccess, hError, payload);
  };

  // Optimized handleClose function
  const handleClose = () => setOpen(false);

  // Function to handle fetching of dependent data based on Sales Organization selection
  const salesOrgDependentOrgElement = (salesOrg) => {
    if (displayedFields.includes("Distribution Channel")) {
      const hSuccess = (data) => setDistributionChannelLookupData(data?.body);
      const hError = (error) => console.error(error);

      doAjax(`/${destination_MaterialMgmt}/data/getDistrChan?salesOrg=${salesOrg.code}`, "get", hSuccess, hError);
    }
  };
  const dataToSend = {
    orgData: ["Plant", "Sales Organization", "Distribution Channel"].map((key) => ({
      info: formData[key] || { code: "", desc: "" }, // Get the formData value or default if not present
      desc: key,
    })),
    selectedViews: { selectedSections },
  };

  // Update form data based on field selection
  const handleFieldChange = (fieldName, selectedValue) => {
    setFormData((prev) => ({ ...prev, [fieldName]: selectedValue }));

    // If Sales Organization changes, update dependent dropdowns
    if (fieldName === "Sales Organization") {
      salesOrgDependentOrgElement(selectedValue);
    }
  };
  const currentDate = `/Date(${Date.now()})/`
  // Handle request header saving
  const handleButtonClick = () => {
    let workflowRole = getRoleForWorkflow(payloadFields?.Region, roles);
    dispatch(setUserDetails({ ...userData, role: workflowRole }));
    setDialogOpen(false);
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const payload = {
      RequestId: requestHeaderData?.requestId ? requestHeaderData?.requestId : "",
      Region: payloadFields?.Region || "",
      MatlType: payloadFields?.MatlType || "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      Division: payloadFields?.Division || "",
      RequestStatus: "DRAFT",
      RequestPriority: payloadFields?.RequestPriority || "",
      LeadingCat: payloadFields?.LeadingCat || "",
      FieldName: payloadFields?.FieldName?.join("$^$") || "",
      TemplateName: payloadFields?.TemplateName || ""
    };

    const hSuccess = (data) => {
      setSuccessMsg(true);
      setMessageDialogMessage(`Request Header Created Successfully with request ID ${appendPrefixByJavaKey(payloadFields?.RequestType, data?.body?.requestId)}`);
      setAlertType("success");
      handleSnackBarOpen();
      dispatch(setRequestHeader(data.body));
      dispatch(setPayload({ keyName: REQUEST_HEADER_FILED_NAMES.REQUEST_ID, data: data?.body?.requestId}))
      setIsAttachmentTabEnabled(true);
      setDisableProceed(false);
      dispatch(updateAllTabsData({}))
      dispatch(setTaskData({}))
      if (initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return
      }
      if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
      if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
        const filteredConfig = filterConfigData(changeData?.["Config Data"], initialPayload?.FieldName, ["Material", "Plant", "Sales Org", "Distribution Channel", "Warehouse", "MRP Controller"]);
        dispatch(changeTemplateDT({ ...changeData, "Config Data": filteredConfig }));
        const filteredTableNames = filterFieldNameData(changeData?.[initialPayload?.TemplateName], initialPayload?.FieldName)
        dispatch(setChangeTableData([...filteredTableNames]));
      }
      // if (initialPayload?.RequestType?.code === "Change") {
      setTimeout(() => {
        dispatch(setTabValue(1));
        setIsSecondTabEnabled(true);
      }, 2500); 
    };
    const hError = () => {
      setSuccessMsg(true);
      setAlertType("error");
      setMessageDialogMessage("Error occured while saving Request Header");
      handleSnackBarOpen();
    };

    doAjax(`/${destination_MaterialMgmt}/alter/createRequestHeader`, "post", hSuccess, hError, payload);
  };

  useEffect(() => {
    if(downloadClicked) {
      if (initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return
      }
      if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
    }
  }, [downloadClicked])

  function checkOrgValues(array) {
    return array.every(item => {
      return item.info.code && item.info.desc;
    });
  }

  const handleCheckValidationError = () => {
    const hasEmptyOrgData = checkOrgValues(dataToSend.orgData);
    if (!hasEmptyOrgData) {
      setSuccessMsg(true)
      setAlertType("error");
      setMessageDialogMessage(`Please choose all mandatory fields`);
      handleSnackBarOpen();
    }
    else {
      const attachView = { label: "Attachments & Comments", value: "attachments&comments" };
      const giView = { label: "General Information", value: "generalInformation" };

      // Using selectedSections as the variable holding selected views instead of undefined value
      const modifiedViews = [giView, ...selectedSections, attachView];

      // Update selectedViews with the array directly
      dataToSend.selectedViews = modifiedViews;

      // Dispatching the updated data
      dispatch(newMaterialData(dataToSend));

      // Navigating to the next page with the updated data
      dispatch(setTabValue(1));
      setIsSecondTabEnabled(true);
    }
  };

  useEffect(() => {
    getRequestHeaderTemplate();
  }, [initialPayload?.RequestType]);

  const getMaterialNo = (value = "",) => {
    const payload = {
      materialNo: value ?? "",
      top: 500,
      skip: 0,
      salesOrg:
        regionBasedSalesOrgData?.uniqueSalesOrgList
          ?.map((item) => item.code)
          ?.join("$^$") || "",
    };
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        dispatch(setDropDown({ keyName: DT_FIELDS_NAME.RETURN_MAT_NUMBER, data: data?.body }));
        dispatch(setDropDown({ keyName: DT_FIELDS_NAME.PARENT_MAT_NUMBER, data: data?.body }));
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SEARCH_PARAMS_MATERIAL_NO}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
  if(regionBasedSalesOrgData?.uniqueSalesOrgList){
    getMaterialNo()
  }
  },[])


  const fetchDivisionData = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REGION_DIVISION_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": region || "",
        },
      ],
    };
    getDtCall(payload);
  }
  useEffect(() => {
    if (initialPayload?.Region) {
      fetchOrgData();
      fetchDivisionData(initialPayload?.Region);
    }
  }, [initialPayload?.Region]);
  // useEffect(() => {
  //   if (location.pathname.includes("ProfitCenterRequestTab")) {
  //     getRequestHeaderTemplatePc();
  //   } else {
  //     getRequestHeaderTemplate();
  //   }
  // }, [initialPayload?.RequestType, location.pathname]);

  useEffect(() => {
    if (initialPayload?.TemplateName) {
      if(initialPayload?.TemplateName === TEMPLATE_KEYS.MRP || initialPayload?.TemplateName === TEMPLATE_KEYS.WARE_VIEW_2){
        dispatch(setPayload({ keyName: "FieldName", data: undefined }));
      }
      getChangeTemplate();
    }
  }, [initialPayload?.TemplateName]);

  // useEffect(() => {
  //   if (isError) {
  //     setMessageDialogMessage(ERROR_MESSAGES?.USER_ACCESS_ERROR);
  //     setMessageDialogTitle("Warning");
  //     setMessageDialogSeverity("danger");
  //     handleMessageDialogClickOpen();
  //   }
  // }, [isError]);

  // useEffect(() => {
  //   if ((initialPayload?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
  //     initialPayload?.RequestType === REQUEST_TYPE.CHANGE) && !isWorkspace
  //   ) {
  //     getUserAccessData();
  //   }
  // }, [initialPayload?.RequestType]);



  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderDetails).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography sx={{ fontSize: "12px", fontWeight: "700", paddingBottom: "10px" }}>{t(key)}</Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterField
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={dropDownData}
                      disabled={isWorkspace || requestHeaderData?.requestId}
                      requestHeader={true}
                    />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && (
              <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
                <Button variant="contained" color="primary" disabled={!checkAllFieldsFilled()} onClick={handleButtonClick}>
                  {t("Save Request Header")}
                </Button>
              </Box>
            )}
            <ToastContainer />
          </Grid>
        ))}

        <Dialog open={open} onClose={handleClose}>
          <DialogTitle sx={{ backgroundColor: "#EAE9FF" }}>Select Org Data</DialogTitle>
          <DialogContent>
            <Grid container columnSpacing={1}>
              {displayedFields.map((fieldName, index) => {
                return (
                  <Fragment key={index}>
                    <Grid item md={4}>
                      <Typography>
                        {fieldName}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item md={8}>
                      <Autocomplete
                        options={fieldName === "Distribution Channel" ? distributionChannelLookupData : dropDownData[fieldName] || []}
                        size="small"
                        getOptionLabel={(option) => `${option.code} - ${option.desc}`}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Typography>{`${option.code} - ${option.desc}`}</Typography>
                          </li>
                        )}
                        onChange={(e, newValue) => handleFieldChange(fieldName, newValue)}
                        renderInput={(params) => <TextField {...params} placeholder={`Select ${fieldName}`} />}
                      />
                    </Grid>
                  </Fragment>
                )
              })}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="outlined">
              {t("Cancel")}
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                handleCheckValidationError();
              }}
            >
              {t("Proceed")}
            </Button>
          </DialogActions>
        </Dialog>

        {dialogOpen && (<RequestDetailsForChange downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked}/>)}
        
        <DownloadDialog
          onDownloadTypeChange={onDownloadTypeChange}
          open={openDownloadDialog}
          downloadType={downloadType}
          handleDownloadTypeChange={handleDownloadTypeChange}
          onClose={handleDownloadDialogClose}
        />
        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />
        {/* {
          isError && (
            <ReusableDialog
              dialogState={openMessageDialog}
              openReusableDialog={handleMessageDialogClickOpen}
              closeReusableDialog={handleMessageDialogClose}
              dialogTitle={messageDialogTitle}
              dialogMessage={messageDialogMessage}
              handleDialogConfirm={handleMessageDialogClose}
              dialogOkText={"OK"}
              dialogSeverity={messageDialogSeverity}
              handleOk={handleOk}
            />
          )
        } */}
        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            alertType={alertType}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}
      </Stack>
    </div>
  );
};

export default RequestHeader;
