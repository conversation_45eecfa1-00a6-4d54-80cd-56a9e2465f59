import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  TextField,
  Button,
  Stack,
  Tooltip,
  IconButton,
  DialogContent,
  DialogActions,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ReusableDataTable from "@components/Common/ReusableTable"
import { setChangeFieldRows, setChangeFieldRowsDisplay, setChangeLogData, setDirectChangeLog, setErrorData, setIsSubmitDisabled, setTemplateArray, updateNewRowIds, updateSelectedRows } from "../../../app/payloadslice";
import { DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, FIELD_TYPE, VISIBILITY_TYPE, ERROR_MESSAGES, REQUEST_TYPE, ENA<PERSON>E_STATUSES, REQUEST_STATUS, TASK_NAME } from "@constant/enum";
import { v4 as uuidv4 } from "uuid";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { CHANGE_TEMPLATES_FIELD_IDENTIFICATION, TEMPLATE_KEYS, TEMPLATE_NAME_MANIPULATION } from "@constant/changeTemplates";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { button_Outlined, button_Primary } from "@components/Common/commonStyles";
import {  
  checkDataValidation,
  convertDate,
  getCurrentDate, 
  getObjectValue, 
  segregateChangeLogData,
} from "@helper/helper";
import { useChangeFieldRows } from '@material/change/hooks/useChangeFieldRows';
import  useDisplayCall  from '@hooks/useDisplayCall';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import useLogger from "@hooks/useLogger";
import moment from 'moment';
import ReusableBackDrop from '@components/Common/ReusableBackDrop';
import { colors } from "@constant/colors";
import { useLocation } from "react-router-dom";
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined';
import ArrowCircleLeftOutlinedIcon from '@mui/icons-material/ArrowCircleLeftOutlined';
import { updatePage, updateNextButtonStatus } from "@app/paginationSlice";
import SingleSelectDropdown from '@components/Common/ui/dropdown/SingleSelectDropdown'
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import AddIcon from '@mui/icons-material/Add'
import useLang from "@hooks/useLang";

const InputCell = ({ params, field, isFieldError, isFieldDisable, isNewRow, keyName, handleChangeValue, handleRemoveError, charCount, setCharCount, isFocused, setIsFocused }) => {
  const [localValue, setLocalValue] = useState(params.row[field.jsonName] || "");
  const rowId = params.row.id;
  const isMaxLengthReached = charCount[keyName] === field?.maxLength;
 
  const handleChangeValueInTable = (event) => {
     const inputValue = event.target.value;
    setLocalValue(inputValue);
    handleChangeValue(
      params.row,
      rowId,
      field.jsonName,
      inputValue?.toUpperCase() || "",
      field.viewName,
      field.fieldName,
      keyName
    );
    setCharCount((prev) => ({ ...prev, [keyName]: inputValue.length }));
  };
 
  return (
    <Tooltip title={params.row[field.jsonName]?.toUpperCase()} arrow placement="top">
      <TextField
        fullWidth
        placeholder={`ENTER ${field.fieldName.toUpperCase()}`}
        variant="outlined"
        size="small"
        value={localValue}
        disabled={isFieldDisable || (!isNewRow && field.visibility === VISIBILITY_TYPE.DISPLAY)}
        inputProps={{ maxLength: field.maxLength, style: { textTransform: "uppercase" } }}
        InputProps={{
          sx: {
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: isFieldError ? colors.error.dark : undefined,
            },
            "&.Mui-disabled": {
              "& input": {
                WebkitTextFillColor: colors.text.primary,
                color: colors.text.primary,
              }
            }
          },
        }}
        onFocus={(e) => {
          e.stopPropagation();
          setIsFocused({ ...isFocused, [keyName]: true });
          setCharCount(prev => ({ ...prev, [keyName]: e.target.value.length }));
          isFieldError && handleRemoveError(rowId, field.fieldName);
        }}
        onKeyDown={e => e.key === ' ' && e.stopPropagation()}
        onClick={e => e.stopPropagation()}
        onChange={handleChangeValueInTable}
        onBlur={() => setIsFocused({ ...isFocused, [keyName]: false })}
        helperText={isFocused[keyName] && (
          isMaxLengthReached
            ? "Max Length Reached"
            : `${charCount[keyName] || 0}/${field.maxLength}`
        )}
        FormHelperTextProps={{
          sx: {
            color: isMaxLengthReached ? colors.error.dark : colors.primary.darkPlus,
            position: "absolute",
            bottom: "-20px",
          },
        }}
        sx={{
          "& .MuiInputBase-root": { height: "34px" },
          "& .MuiOutlinedInput-root": {
            "&.Mui-focused fieldset": {
              borderColor: isMaxLengthReached ? colors.error.dark : "",
            },
          },
        }}
      />
    </Tooltip>
  );
};

const ContentForChange = (props) => {
  const { customError } = useLogger();
  const { getNextDisplayDataForChange } = useDisplayCall();
  const changeData = useSelector((state) => state.tabsData.changeFieldsDT);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const configData = changeData?.["Config Data"];
  const tablesData = useSelector((state) => state.payload.tablesList);
  const changeFieldRows = useSelector((state) => state.payload.changeFieldRows)
  const changeFieldRowsDisplay = useSelector((state) => state.payload.changeFieldRowsDisplay)
  const changeLogData = useSelector((state) => state.payload.changeLogData);
  const matNos = useSelector((state) => state.payload.matNoList)
  const newRowIds = useSelector((state) => state.payload.newRowIds)
  const dropDownData = useSelector((state) => state.materialDropDownData.dropDown || {})
  const isLoading = useSelector((state) => state.payload.dataLoading)
  const errorData = useSelector((state) => state.payload.errorData)
  const selectedRowData = useSelector((state) => state.payload.selectedRows)
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const userMangmentData = useSelector((state) => state.userManagement.userData);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const paginationData = useSelector((state) => state.paginationData);
  const templateArray = useSelector((state) => state.payload.templateArray);
  const requestorPayload = useSelector((state) => state.payload.requestorPayload);
  const [changeFieldRowsCopy, setChangeFieldRowsCopy] = useState([]);
  const [isFocused, setIsFocused] = useState({});
  const [charCount, setCharCount] = useState({});
  const [action, setAction] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const isLoadingFromTabSlice = useSelector((state) => state.tabsData.dataLoading);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ data: {}, isVisible: false });
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");
  const { t } = useLang();
  let reqBenchData = location.state;


  useEffect(() => {
    if (changeFieldRows && changeFieldRowsCopy?.length === 0) {
      setChangeFieldRowsCopy(JSON.parse(JSON.stringify(changeFieldRows)));
    }
  }, [changeFieldRows]);

  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };
  const handlePageSizeChange = (event) => {
    
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleAddRow = () => {
    const fieldKeys = changeFieldRows?.length > 0 ? Object.keys(changeFieldRows[0]) : [];
    const newRow = fieldKeys?.reduce((acc, key) => {
      acc[key] = key === "id" 
        ? uuidv4() 
        : key === "slNo" 
          ? 1 
          : "";
      return acc;
    }, {});

    const updatedRows = [newRow, ...changeFieldRows]?.map((row, index) => ({
      ...row,
      slNo: index + 1,
    }));
    const updatedPageRows = [
      newRow,
      ...(changeFieldRowsDisplay[paginationData?.page] || []),
    ].map((row, index) => ({ ...row, slNo: index + 1 }));

    dispatch(updateNewRowIds([newRow?.id, ...newRowIds]));
    dispatch(setChangeFieldRows(updatedRows));
    dispatch(setChangeFieldRowsDisplay({
      ...changeFieldRowsDisplay,
      [paginationData?.page]: updatedPageRows,
    }));
    dispatch(updateSelectedRows([newRow?.id, ...selectedRowData]));
    dispatch(setIsSubmitDisabled(true));
    props?.setCompleted([true, false]);
  };

  const ManipulatedTemplateName = getObjectValue(TEMPLATE_NAME_MANIPULATION,payloadData?.TemplateName)
  const secondParameterForDifferentiation = getObjectValue(CHANGE_TEMPLATES_FIELD_IDENTIFICATION,ManipulatedTemplateName);
  const ThirdParameter = secondParameterForDifferentiation?.length > 1;

  const getThePreviousValues = (params, field) => {
    const foundObject = changeFieldRowsCopy?.find(item => {
      const isMatch =
        item.Material === params?.Material &&
        item?.[secondParameterForDifferentiation[0]] === params?.[secondParameterForDifferentiation[0]];
      if (ThirdParameter) {
        return isMatch && item?.[secondParameterForDifferentiation[1]] === params?.[secondParameterForDifferentiation[1]];
      }
      return isMatch;
    });
    if (foundObject) {
      return foundObject[field];
    }
  };

  const getThePreviousValuesForGroup = (params, field, key, secondParameterForDifferentiationInCaseGroup) => {
    const foundObject = changeFieldRowsCopy?.[key]?.find(item => {
      let isMatch = item.Material === params?.Material;

      if (secondParameterForDifferentiationInCaseGroup?.length > 0) {
        isMatch = isMatch && item?.[secondParameterForDifferentiationInCaseGroup[0]] === params?.[secondParameterForDifferentiationInCaseGroup[0]];
        
        if (secondParameterForDifferentiationInCaseGroup?.length > 1) {
          isMatch = isMatch && item?.[secondParameterForDifferentiationInCaseGroup[1]] === params?.[secondParameterForDifferentiationInCaseGroup[1]];
        }
      }
      
      return isMatch;
    });

    if (foundObject) {
      return foundObject[field];
    }
    return "-";
  };

  const updateTemplateArray = (obj) => {
    dispatch(setTemplateArray(obj));
  };
  const { handleObjectChangeFieldRows } = useChangeFieldRows(
    changeFieldRows,
    changeFieldRowsDisplay,
    paginationData,
    ManipulatedTemplateName,
    userMangmentData,
    requestIdHeader,
    templateArray,
    updateTemplateArray,
    getThePreviousValuesForGroup,
    props?.RequestId,
  )
 
  const handleChangeValue = (params, id, field, value, key, fieldName) => {
    if(Array.isArray(changeFieldRows)) {
      if (field === "AltUnit" || field === "Langu") {
        const validationResult = checkDataValidation(params, changeFieldRowsDisplay?.[paginationData?.page], matNos, value, payloadData?.TemplateName);
    
        if (validationResult === "matError") {
          setAlertType("error");
          setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES?.MATL_ERROR_MSG));
          handleSnackBarOpen();
          return;
        } else if (validationResult === "altUnitError") {
          setAlertType("error");
          setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES?.ALTUNIT_ERROR_MSG));
          handleSnackBarOpen();
          return;
        } else if (validationResult === "languError") {
          setAlertType("error");
          setMessageDialogMessage(t(DIALOUGE_BOX_MESSAGES?.LANG_ERROR_MSG));
          handleSnackBarOpen();
          return;
        }
      }
      const updatedRows = changeFieldRows?.map((row) => 
        row?.id === id 
          ? { 
              ...row, 
              [field]: value, 
              ...(field === "Material" 
                ? {
                    ...(payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC ? { Langu: "" } : {}),
                    ...(payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC ? { AltUnit: "" } : {})
                  } 
                : {}) 
            } 
          : row
      );
      dispatch(setChangeFieldRows(updatedRows));
      
      const updatedRowsDisplay = changeFieldRowsDisplay?.[paginationData?.page]?.map((row) => 
        row?.id === id 
          ? { 
              ...row, 
              [field]: value, 
              ...(field === "Material" 
                ? {
                    ...(payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC ? { Langu: "" } : {}),
                    ...(payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC ? { AltUnit: "" } : {})
                  } 
                : {}) 
            } 
          : row
      );
      dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: updatedRowsDisplay }));
      
    
      const dateInfo = getCurrentDate();
      const formatValueIfDate = (val) => {
        if (val?.toString().startsWith('/Date(') && val?.toString().endsWith(')/')) {
          return convertDate(val);
        }
        return val;
      };
      let obj = {
        "ObjectNo":`${params?.Material}$$${params?.[secondParameterForDifferentiation[0]]}${ThirdParameter ? `$$${params?.[secondParameterForDifferentiation[1]]}`:''}`,
        "ChangedBy": userMangmentData.emailId,
        "ChangedOn": dateInfo.sapFormat,
        "FieldName": fieldName ?? field,
        "PreviousValue": getThePreviousValues(params,field) ?? "-",
        "SAPValue": getThePreviousValues(params,field) ?? "-",
        "CurrentValue": formatValueIfDate(value) ?? '',
      }
      updateTemplateArray(obj);
      let changeLogPayload = {
        RequestId: requestIdHeader ? requestIdHeader : (props?.RequestId).slice(3),
        changeLogId: params?.ChangeLogId ?? null,
        [ManipulatedTemplateName]: [...templateArray, obj]
      }
      const finalChangeLogDataPayload = segregateChangeLogData(changeLogPayload, ManipulatedTemplateName)
      dispatch(setChangeLogData(finalChangeLogDataPayload))
    }
    else if (typeof changeFieldRows === "object" && changeFieldRows[key]) {
      handleObjectChangeFieldRows(key, id, field, value, fieldName);
    }
  };

  // Handle row selection changes
  const onRowsSelectionHandler = (ids, key) => {
    if(Array.isArray(selectedRowData)) {
      dispatch(updateSelectedRows(ids));
      dispatch(setIsSubmitDisabled(true));
      return;
    }
    dispatch(updateSelectedRows({
      ...selectedRowData, [key]: ids
    }));
    dispatch(setIsSubmitDisabled(true));
  };

  const handleRemoveError = (id, fieldName) => {
    const updatedErrorData = {};
    Object.keys(errorData).forEach(key => {
      const error = errorData[key];
      if (error.id === id) {
        const updatedMissingFields = error.missingFields.filter(field => field !== fieldName);
        if (updatedMissingFields.length > 0) {
          updatedErrorData[key] = {
            ...error,
            missingFields: updatedMissingFields
          };
        }
      } else {
        updatedErrorData[key] = { ...error };
      }
    });
    dispatch(setErrorData(updatedErrorData));
  };

const handleDelete = () => {
  const params = isDeleteDialogVisible?.data;
  const rowIdToDelete = params?.row?.id;

  if (Array.isArray(changeFieldRows)) {
    const updatedRows = changeFieldRows.filter((row) => row?.id !== rowIdToDelete);
    const updatedRowsWithSlNo = updatedRows.map((row, index) => ({
      ...row,
      slNo: index + 1,
    }));
    dispatch(setChangeFieldRows(updatedRowsWithSlNo));

    const updatedDisplayRows = {
      ...changeFieldRowsDisplay,
      [paginationData?.page]: changeFieldRowsDisplay[paginationData?.page]?.filter(
        (row) => row?.id !== rowIdToDelete
      )?.map((row, index) => ({
        ...row,
        slNo: index + 1,
      }))
    };
    dispatch(setChangeFieldRowsDisplay(updatedDisplayRows));

    const updatedIds = newRowIds?.filter((id) => id !== rowIdToDelete);
    dispatch(updateNewRowIds(updatedIds));

    const deletedRow = changeFieldRows.find(row => row.id === rowIdToDelete);
    if (deletedRow) {
      const objectNoToDelete = `${deletedRow.Material}$$${deletedRow[secondParameterForDifferentiation[0]]}${ThirdParameter ? `$$${deletedRow[secondParameterForDifferentiation[1]]}` : ''}`;

      const updatedChangeLogData = JSON.parse(JSON.stringify(changeLogData));

      if (updatedChangeLogData[deletedRow.Material]?.[ManipulatedTemplateName]) {
        const filteredLogs = updatedChangeLogData[deletedRow.Material][ManipulatedTemplateName].filter(
          log => log.ObjectNo !== objectNoToDelete && log.ObjectNo !== `${deletedRow.Material}$$`
        );

        if (filteredLogs.length === 0) {
          delete updatedChangeLogData[deletedRow.Material][ManipulatedTemplateName];

          if (Object.keys(updatedChangeLogData[deletedRow.Material]).length === 0) {
            delete updatedChangeLogData[deletedRow.Material];
            delete updatedChangeLogData[""];
          }
        } else {
          updatedChangeLogData[deletedRow.Material][ManipulatedTemplateName] = filteredLogs;
        }
      }
      dispatch(setDirectChangeLog(updatedChangeLogData));
    }
  }
  setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
}
  // else if (typeof changeFieldRows === "object") {
  //   if (params?.row?.type === "Basic Data" || params?.row?.type === "Basic Data") {
  //     const updatedChangeFieldRows = Object?.keys(changeFieldRows).reduce((acc, key) => {
  //       const filteredRows = changeFieldRows[key]?.filter((row) => row?.Material !== params?.row?.Material);
  //       acc[key] = filteredRows.map((row, index) => ({ ...row, slNo: index + 1 }));
  //       return acc;
  //     }, {});
  
  //     dispatch(setChangeFieldRows(updatedChangeFieldRows));
  //   } else {
  //     const updatedKeyRows = changeFieldRows[params?.row?.type]?.filter((row) => row?.id !== rowIdToDelete);
  //     const updatedKeyRowsWithSlNo = updatedKeyRows.map((row, index) => ({
  //       ...row,
  //       slNo: index + 1,
  //     }));
  
  //     dispatch(setChangeFieldRows({ ...changeFieldRows, [params?.row?.type]: updatedKeyRowsWithSlNo }));
  //   }
  // }


  const renderTable = (key, data) => {
    const columns = [   
      {
        headerName: "Sl. No.",
        field: "slNo",
        align: "center",
        flex: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || 
               (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && key === "Plant Data"))
               ? undefined
               : 0.1,
        width: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || 
                (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && key === "Plant Data"))
                ? 1
                : undefined,
      },
      ...data.map((field) => ({
        headerName: (
          <span>
            {field.fieldName}
            {field.visibility === VISIBILITY_TYPE?.MANDATORY && (
              <span style={{ color: colors?.error?.dark, marginLeft: 4 }}>*</span>
            )}
          </span>
        ),
        field: field.jsonName,
        flex: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && field?.viewName === "Plant Data")) ? undefined : 1,
        width: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && field?.viewName === "Plant Data")) ? 200 : undefined,
        renderCell: (params) => {
          const rowError = Object?.values(errorData)?.find(
            (error) => error?.id === params?.row?.id
          );
          const keyName = `${params?.row?.id}-${field?.jsonName}`;
          const isFieldError = rowError?.missingFields?.includes(field?.fieldName);
          const isNewRow = newRowIds?.includes(params?.row?.id);
          const isFieldDisable = ((isreqBench && !ENABLE_STATUSES?.includes(reqBenchData?.reqStatus))) ? true : false

          if (field.fieldType === FIELD_TYPE.INPUT) {
            return (
              <InputCell
                params={params}
                field={field}
                isFieldError={isFieldError}
                isFieldDisable={isFieldDisable}
                isNewRow={isNewRow}
                keyName={keyName}
                handleChangeValue={handleChangeValue}
                handleRemoveError={handleRemoveError}
                charCount={charCount}
                setCharCount={setCharCount}
                isFocused={isFocused}
                setIsFocused={setIsFocused}
              />
            );
          } else if (field.fieldType === FIELD_TYPE.DROPDOWN) {
            const isNewRow = newRowIds?.includes(params?.row?.id);
            const fieldValue = (field?.jsonName !== "Unittype1" && field?.jsonName !== "Spproctype" && field?.jsonName !== "MrpCtrler") ?
              dropDownData?.[field?.jsonName]?.find((item) => 
                item.code === params?.row?.[field?.jsonName]) :
              (field?.jsonName === "Spproctype" || field?.jsonName === "MrpCtrler") ?
              dropDownData?.[field?.jsonName]?.[params?.row?.Plant]?.find((item) =>
                item.code === params?.row?.[field?.jsonName]) :
              dropDownData?.[field?.jsonName]?.[params?.row?.WhseNo]?.find((item) =>
                item.code === params?.row?.[field?.jsonName])

            return (
                <SingleSelectDropdown
                  options={
                    field?.jsonName === "Unittype1"
                  ? dropDownData?.Unittype1?.[params?.row?.WhseNo]
                  : field?.jsonName === "Spproctype"
                  ? dropDownData?.Spproctype?.[params?.row?.Plant] 
                  : field?.jsonName === "MrpCtrler"
                  ? dropDownData?.MrpCtrler?.[params?.row?.Plant] 
                  : dropDownData?.[field?.jsonName] ? dropDownData?.[field?.jsonName] : []
                  }
                  value={
                    fieldValue
                    ? fieldValue
                    : params?.row?.[field?.jsonName]
                      ? {code: params?.row?.[field?.jsonName], desc: ""}
                      : null
                    }
                  onChange={(newValue) => {
                    handleChangeValue(
                      params.row,
                      params.row.id,
                      field?.jsonName,
                      newValue?.code,
                      key,
                      field?.fieldName
                    );
                    if (isFieldError) {
                      handleRemoveError(params.row.id, field?.fieldName);
                    }
                  }}
                  listWidth={150}
                  placeholder={`Select ${field.fieldName}`}
                  disabled={isFieldDisable ? true : isNewRow ? false : field?.visibility === VISIBILITY_TYPE?.DISPLAY}
                  isFieldError={isFieldError}              
                />
            );
          }else if (field.fieldType === FIELD_TYPE.DATE_FIELD) {
            const isNewRow = newRowIds?.includes(params?.row?.id);
            const fieldValue = params?.row?.[field?.jsonName] 
              ? (() => {
                  const dateString = params?.row?.[field?.jsonName];
                  if (dateString.startsWith('/Date(') && dateString.endsWith(')/')) {
                    const timestamp = parseInt(dateString.slice(6, -2));
                    return new Date(timestamp);
                  }
                  if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}/)) {
                    return new Date(dateString);
                  }
                  return moment(dateString, ['YYYY-MM-DD HH:mm:ss.S', 'DD MMM YYYY HH:mm:ss UTC']).toDate();
                })()
              : null;
            return (
              <Tooltip title={params?.row?.[field?.jsonName]} arrow placement="top">
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    disabled={isFieldDisable? true :isNewRow ? false : field?.visibility === VISIBILITY_TYPE?.DISPLAY}
                    slotProps={{ 
                      textField: { 
                        size: "small",
                        fullWidth: true,
                        InputProps: {
                          sx: {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: isFieldError ? colors?.error?.dark : undefined,
                            },
                          },
                        }
                      } 
                    }}
                    value={fieldValue}
                    onChange={(newValue) => {
                      if (newValue) {
                        const sapDateFormat = `/Date(${Date.parse(newValue)})/`;
                        handleChangeValue(
                          params.row,
                          params.row.id,
                          field?.jsonName,
                          sapDateFormat,
                          key,
                          field?.fieldName
                        );
                      } else {
                        handleChangeValue(
                          params.row,
                          params.row.id,
                          field?.jsonName,
                          null,
                          key,
                          field?.fieldName
                        );
                      }
                      if (isFieldError) {
                        handleRemoveError(params.row.id, field?.fieldName);
                      }
                    }}
                    onError={(error) => {
                      if (error && !isFieldError) {
                        customError(ERROR_MESSAGES.DATE_VALIDATION_ERROR, error);
                      }
                    }}
                    maxDate={new Date(9999, 11, 31)}
                  />
                </LocalizationProvider>
              </Tooltip>
            );
          }
           else {
            return params.value || "-";
          }
        },
      })),
      {
        ...((((payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC) && !isWorkspace) ||
        ((payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC) && isWorkspace && (taskData?.taskDesc === TASK_NAME?.REQUESTOR || reqBenchData?.reqStatus === REQUEST_STATUS?.DRAFT))
        ) && 
          {
            field: "action",
            headerName: "Action",
            flex: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && field?.viewName === "Plant Data")) ? undefined : 1,
            width: (payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || (payloadData?.TemplateName === TEMPLATE_KEYS?.MRP && field?.viewName === "Plant Data")) ? 200 : undefined,
            align: "center",
            headerAlign: "center",
            renderCell: (params) => {
              return (
                <Stack direction="row" alignItems="center" sx={{ marginLeft: "0.5rem", magrinRight: "0.5rem" }} spacing={0.5}>
                    <Tooltip title="Delete Row">
                      <IconButton
                        disabled={!newRowIds?.includes(params?.row?.id)}
                        onClick={() => {
                          setIsDeleteDialogVisible({data:params, isVisible:true})
                        }}
                        color="error"
                      >
                        <DeleteOutlineOutlinedIcon />
                      </IconButton>
                    </Tooltip>
                </Stack>
              );
            },
          })
      }
    ];

    const rowData = Array.isArray(changeFieldRows)
    ? changeFieldRowsDisplay?.[paginationData?.page] || []
    : changeFieldRowsDisplay?.[paginationData?.page]?.[key] || []

    const selectedRows = Array.isArray(selectedRowData)
    ? selectedRowData
    : selectedRowData[key];

    return (
      <div style={{ height: 400, width: "100%" }}>
        <ReusableDataTable
          paginationLoading={isLoading}
          rows={rowData}
          rowCount={rowData?.length ?? 0}
          columns={columns}
          getRowIdValue={"id"}
          rowHeight={70}
          isLoading={isLoading}
          tempheight='calc(100vh - 380px)'
          page={page}
          pageSize={pageSize}
          selectionModel={selectedRows}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onCellEditCommit={handleChangeValue}
          checkboxSelection={!(isreqBench && !ENABLE_STATUSES?.includes(reqBenchData?.reqStatus))}
          // onRowsSelectionHandler={(selectedIds) => onRowsSelectionHandler(selectedIds, key)}
          disableSelectionOnClick
          showCustomNavigation={true}
          hideFooter={true}
        />
        {isDeleteDialogVisible?.isVisible && (
        <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteOutlineOutlinedIcon size='small' color="error" sx={{fontSize:"20px" }}/>} Title={t("Delete Row")+"!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
            <DialogContent sx={{mt:2}}>
              {t(DIALOUGE_BOX_MESSAGES.DELETE_MESSAGE)}
            </DialogContent>
            <DialogActions>
              <Button variant="outlined" size="small" sx={{...button_Outlined}} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
                {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
              <Button variant="contained" size="small" sx={{ ...button_Primary}} onClick={handleDelete}>{t(DELETE_MODAL_BUTTONS_NAME.DELETE)}</Button>
            </DialogActions>
        </CustomDialog>
      )}
      </div>
    );
  };
  const keys = configData && Object.keys(configData);

  useEffect(() => {
    if (paginationData?.page+1 && (payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD)) { 
      if(isWorkspace && (!requestorPayload || Object?.keys(requestorPayload)?.length === 0)) {
        getNextDisplayDataForChange("display");
        setPage(0);
      }
      else {
        getNextDisplayDataForChange("requestor");
        setPage(0);
      }

      if(action === "prev") {
        dispatch(updateNextButtonStatus(false))
      }
      else if(action === "next") {
        if(paginationData?.currentElements >= paginationData?.totalElements) {
          dispatch(updateNextButtonStatus(true))
        }
      }
    }
  }, [paginationData?.page]);

  // Single key-value pair: Render a single table
  if (keys?.length === 1) {
    const key = keys[0];
    const data = configData[key];
    return (
      <Stack>
        <Stack direction ="row" justifyContent="space-between" mb={1.5}>
        {
          (((payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC) && !isWorkspace) ||
          ((payloadData?.TemplateName === TEMPLATE_KEYS?.LOGISTIC || payloadData?.TemplateName === TEMPLATE_KEYS?.UPD_DESC) && isWorkspace && (taskData?.taskDesc === TASK_NAME?.REQUESTOR || reqBenchData?.reqStatus === REQUEST_STATUS?.DRAFT))
          ) ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddRow}
              startIcon={<AddIcon />}
              sx={{ 
                borderRadius: "10px",
                boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.15)",
              }}
            >
              Add Row
            </Button>
          ) : <Box sx={{ width: 0, height: 0 }} />
        }
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backgroundImage: "linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",
            border: "1px solid #E0E0E0",
            padding: "5px",
            borderRadius: "10px",
            mt: -1,
            boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.08)",
          }}
        >
          <Tooltip title="Previous" placement="top" arrow>
            <IconButton
              disabled={paginationData?.page === 0 || false}
              onClick={() => {
                setAction("prev");
                dispatch(updatePage(paginationData?.page - 1));
              }}
            >
              <ArrowCircleLeftOutlinedIcon
                sx={{
                  color:
                    paginationData?.page === 0
                      ? colors?.secondary?.grey
                      : colors?.primary?.main,
                  fontSize: "1.5rem",
                  marginRight: "2px",
                }}
              />
            </IconButton>
          </Tooltip>

          <span style={{ marginRight: "2px" }}>
            <strong style={{ color: colors?.primary?.main }}>Materials :</strong>{" "}
            <strong>
              {paginationData?.page * paginationData?.size + 1} -{" "}
              {paginationData?.currentElements}
            </strong>{" "}
            <span>of</span>{" "}
            <strong>{paginationData?.totalElements}</strong>
          </span>

          <Tooltip title="Next" placement="top" arrow>
            <IconButton
              disabled={
                paginationData?.currentElements >= paginationData?.totalElements || false
              }
              onClick={() => {
                setAction("next");
                dispatch(updatePage(paginationData?.page + 1));
              }}
            >
              <ArrowCircleRightOutlinedIcon
                sx={{
                  color:
                    paginationData?.currentElements >= paginationData?.totalElements
                      ? colors?.secondary?.grey
                      : colors?.primary?.main,
                  fontSize: "1.5rem",
                }}
              />
            </IconButton>
          </Tooltip>
        </Box>
        </Stack>
        <div>
          {renderTable(key, data)}
        </div>
        <ReusableSnackBar
          openSnackBar={openSnackbar} 
          alertMsg={messageDialogMessage} 
          alertType={alertType} 
          handleSnackBarClose={handleSnackBarClose} 
        />
      </Stack>
    )
  }

  // Multiple key-value pairs: Render accordions with tables inside
  return (
    <>
      <ReusableBackDrop 
        blurLoading={isLoadingFromTabSlice}
      />
      {!isLoadingFromTabSlice && (
        <>
          {!configData ? (
            <Typography>No data available</Typography>
          ) : (

            <div>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                backgroundImage: "linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",
                border: "1px solid #E0E0E0",
                borderRadius: "10px",
                padding: "5px",
                width: "fit-content",
                marginLeft: "auto",
                mt: -1,
                mb: 2,
                boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.08)",
              }}
            >
              <Tooltip title="Previous" placement="top" arrow>
                <IconButton
                  disabled={paginationData?.page === 0 || false}
                  onClick={() => {
                    dispatch(updatePage(paginationData?.page - 1));
                  }}
                >
                  <ArrowCircleLeftOutlinedIcon
                    sx={{
                      color:
                        paginationData?.page === 0
                          ? colors?.secondary?.grey
                          : colors?.primary?.main,
                      fontSize: "1.5rem",
                      marginRight: "2px",
                    }}
                  />
                </IconButton>
              </Tooltip>

              <span style={{ marginRight: "2px" }}>
                <strong style={{ color: colors?.primary?.main }}>Materials :</strong>{" "}
                <strong>
                  {paginationData?.page * paginationData?.size + 1} -{" "}
                  {paginationData?.currentElements}
                </strong>{" "}
                <span>of</span>{" "}
                <strong>{paginationData?.totalElements}</strong>
              </span>

              <Tooltip title="Next" placement="top" arrow>
                <IconButton
                  disabled={
                    paginationData?.currentElements >= paginationData?.totalElements || false
                  }
                  onClick={() => {
                    dispatch(updatePage(paginationData?.page + 1));
                  }}
                >
                  <ArrowCircleRightOutlinedIcon
                    sx={{
                      color:
                        paginationData?.currentElements >= paginationData?.totalElements
                          ? colors?.secondary?.grey
                          : colors?.primary?.main,
                      fontSize: "1.5rem",
                    }}
                  />
                </IconButton>
              </Tooltip>
            </Box>
            {keys?.map((key) =>
              tablesData?.includes(key) ? (
                <Accordion key={key} sx={{ marginBottom: "20px", boxShadow: 3 }}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${key}-content`}
                    id={`${key}-header`}
                    sx={{
                      backgroundImage:
                        "linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",
                      padding: "8px 16px",
                      "&:hover": {
                        backgroundImage:
                          "linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)",
                      },
                    }}
                  >
                    <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                      {key}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ height: "calc(100vh - 300px)" }}>
                    {renderTable(key, configData[key])}
                  </AccordionDetails>
                </Accordion>
              ) : null
            )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default ContentForChange;
