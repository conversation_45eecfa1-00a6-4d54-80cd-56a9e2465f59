import React, { useState, useEffect } from "react";
import { Box, Typography, Tooltip, IconButton, Tabs, Tab } from "@mui/material";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import { colors } from "@constant/colors";
import BOMViewsTable from "./BOMViewsTable";
import { useDispatch, useSelector } from "react-redux";
import { setTabRows } from "@BillOfMaterial/bomSlice";

const BOMViews = ({ isTabsZoomed, toggleTabsZoom, t, viewNames, viewsDt, selectedRowID }) => {
  const [activeTab, setActiveTab] = useState(0);
  const dispatch = useDispatch();
  

  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const currentRowTabData = tabFieldValues[selectedRowID] || {};


  useEffect(() => {
    if (!selectedRowID) return;

    const docRows = Array.isArray(tabFieldValues[selectedRowID]?.Document)
      ? tabFieldValues[selectedRowID].Document
      : [];
    const matRows = Array.isArray(tabFieldValues[selectedRowID]?.Material)
      ? tabFieldValues[selectedRowID].Material
      : [];
    const totalRows = matRows.length + docRows.length;

    let generalFields = [];
    if (viewsDt) {
      generalFields = viewsDt
        .flatMap(item => item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)
        .filter(field => field.MDG_MAT_VIEW_NAME === "General")
        .map(field => field.MDG_MAT_JSON_FIELD_NAME || field.MDG_MAT_UI_FIELD_NAME);
    }

    const generalRows = tabFieldValues[selectedRowID]?.General || [];

    if (generalRows.length !== totalRows) {
      const newRows = Array.from({ length: totalRows }, (_, i) => {
        const row = { id: i + 1 };
        generalFields.forEach(field => row[field] = "");
        return row;
      });
      dispatch(setTabRows({
        rowId: selectedRowID,
        viewName: "General",
        rows: newRows
      }));
    }
  }, [tabFieldValues, selectedRowID, viewsDt, dispatch]);

  const handleTabChange = (event, newTab) => {
    setActiveTab(newTab);
  };

  const handleRowsUpdate = (newRows) => {
    const activeViewName = viewNames[activeTab];
    dispatch(setTabRows({
      rowId: selectedRowID,
      viewName: activeViewName,
      rows: newRows
    }));
  };

  const renderTabContent = () => {
    const activeViewName = viewNames[activeTab];
    const currentRows = currentRowTabData[activeViewName] || [];
    
    return (
      <BOMViewsTable 
        viewsDt={viewsDt} 
        activeViewName={activeViewName} 
        rows={currentRows}
        onRowsUpdate={handleRowsUpdate}
      />
    );
  };

  return (
    <Box
      sx={{
        position: isTabsZoomed ? "fixed" : "relative",
        top: isTabsZoomed ? 0 : "auto",
        left: isTabsZoomed ? 0 : "auto",
        right: isTabsZoomed ? 0 : "auto",
        bottom: isTabsZoomed ? 0 : "auto",
        width: isTabsZoomed ? "100vw" : "100%",
        height: isTabsZoomed ? "100vh" : "auto",
        zIndex: isTabsZoomed ? 1004 : undefined,
        backgroundColor: isTabsZoomed ? "white" : "transparent",
        padding: isTabsZoomed ? "20px" : "0",
        marginTop: "20px",
        display: "flex",
        flexDirection: "column",
        boxShadow: isTabsZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
        transition: "all 0.3s ease",
        borderRadius: "8px",
        border: "1px solid #e0e0e0",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "8px 16px",
          borderRadius: "8px 8px 0 0",
        }}
      >
        <Typography variant="h6">{t("BOM Details")}</Typography>
        <Tooltip
          title={isTabsZoomed ? t("Exit Zoom") : t("Zoom In")}
          sx={{ zIndex: "1009" }}
        >
          <IconButton
            onClick={toggleTabsZoom}
            color="primary"
            sx={{
              backgroundColor: "rgba(0, 0, 0, 0.05)",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.1)",
              },
            }}
          >
            {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
          </IconButton>
        </Tooltip>
      </Box>
      <Box sx={{ display: "flex", flexDirection: "column" }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            top: 0,
            position: "sticky",
            zIndex: 1000,
            backgroundColor: colors.background.container,
            borderBottom: `1px solid ${colors.border.light}`,
            "& .MuiTab-root": {
              minHeight: "48px",
              textTransform: "none",
              fontSize: "14px",
              fontWeight: 600,
              color: colors.black.graphite,
              "&.Mui-selected": {
                color: colors.primary.main,
                fontWeight: 700,
              },
              "&:hover": {
                color: colors.primary.main,
                opacity: 0.8,
              },
            },
            "& .MuiTabs-indicator": {
              backgroundColor: colors.primary.main,
              height: "3px",
            },
          }}
        >
          {viewNames.map((viewName, index) => (
            <Tab key={index} label={viewName} />
          ))}
        </Tabs>

        <Box
          sx={{
            padding: 2,
            marginTop: 2,
            flexGrow: 1,
            overflow: "auto",
            height: isTabsZoomed ? "calc(100vh - 180px)" : "auto",
          }}
        >
          {renderTabContent()}
        </Box>
      </Box>
    </Box>
  );
};

export default BOMViews;
