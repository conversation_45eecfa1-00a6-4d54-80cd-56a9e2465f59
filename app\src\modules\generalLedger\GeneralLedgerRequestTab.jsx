import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import RequestHeaderGl from "./RequestHeaderGl";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import { setActiveStep } from "../../app/redux/stepperSlice";
import RequestDetailsChangeGL from "./RequestDetailsChangeGL";
import ExcelOperationsCard from "../../components/Common/ExcelOperationsCard";
import { setRequestHeader } from "@app/requestDataSlice";
import CustomDialog from "@components/Common/ui/CustomDialog";
import {
  destination_GeneralLedger_Mass,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import {
  transformApiResponseToReduxPayloadGl
} from "../../functions";
import { Step, Step<PERSON><PERSON>on, Stepper, IconButton, Button, DialogContent, DialogActions } from "@mui/material";
import { Box, Grid, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, REQUEST_STATUS, REQUEST_TYPE,LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { setDependentDropdown, setDropDown } from "../../app/dropDownDataSlice";
import RequestDetailsGL from "./RequestDetailsGL";
import { setRequestHeaderPayloadData ,setdropdownDataForExtendedCode,setSelecteddropdownDataForExtendedCode,resetPayloadDataGL} from "@app/generalLedgerTabSlice";
import { setGLPayload, updateModuleFieldDataGL } from "../../app/generalLedgerTabSlice";
import useLang from "@hooks/useLang";
import { setCreatePayloadCopyForChangeLog } from "../../app/changeLogReducer";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import ChangeLogGL from "@components/Changelog/ChangeLogGL";
import { clearChangeLogData } from "../../app/payloadslice";
import { clearCreateChangeLogDataGL, clearCreateTemplateArray } from "@app/changeLogReducer";
import { WarningOutlined } from "@mui/icons-material";
import { colors } from "@constant/colors";
import { button_Outlined, button_Primary } from "../../components/Common/commonStyles.jsx"
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import { setDropDown as setDropDownAction, setOdataApiCall } from "@generalLedger/slice/generalLedgerDropDownSlice";
import { setLocalStorage,clearLocalStorageItem } from "@helper/helper.js";

const steps = ["Request Header", "General Ledger List", "Attachments & Comments", "Preview"];

const GeneralLedgerRequestTab = () => {
  const {t} = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const requestHeaderData = useSelector((state) => state.generalLedger.payload.requestHeaderData);
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader);
  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const location = useLocation();
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([]);
  const navigate = useNavigate();
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [isGLPayloadSet, setIsGLPayloadSet] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const isGeneralLedgerOdataApiCalled = useSelector((state) => state.generalLedgerDropDownData?.isOdataApiCalled)
  const { fetchAllDropdownFMD } = useDropdownFMDData(destination_GeneralLedger_Mass,setDropDownAction);


  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };

  const rowData = location.state;
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const allDropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});
  const handleDownload = () => {
    setDownloadClicked(true);
  }
    const reduxPayload = useSelector((state) => state.generalLedger.payload);

  const { selectedRowId, tabs } = useSelector((state) => state.profitCenterTab);


  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

   const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };


  const handleUploadGL = (file) => {
    let url = "";
    // if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
    url = "getAllGeneralLedgerFromExcel";
    // } else {
    //   url = "getAllProfitCenterFromExcelForMassChange";
    // }
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "MDG_PC_FIELD_CONFIG" : "MDG_CHANGE_TEMPLATE_DT");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "v2" : "v4");
    formData.append("requestId", requestId ? requestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(`/${destination_GeneralLedger_Mass}/massAction/${url}`, "postformdata", hSuccess, hError, formData);
  };


  const getCompanyCode = (coa,glID,CompanyCode,coCodetoExtend) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
      let filteredCompanyCodeForExtend=data?.body?.filter(
      (item) => item.code !== CompanyCode)
        const selectedCodes = coCodetoExtend?.split(",");

        if(selectedCodes?.length > 0){

          const preselectedOptions = data?.body?.filter(option =>//
            selectedCodes.includes(option.code)
          );
          dispatch(setSelecteddropdownDataForExtendedCode({
            uniqueId: glID, 
            data: preselectedOptions
          }));
        }

      dispatch(setdropdownDataForExtendedCode({
        uniqueId: glID, 
        data: filteredCompanyCodeForExtend
      }));
      
      
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };


   const getSortKey = (glID) => {
      const hSuccess = (data) => {
        //setDropdownDataLanguage(data.body);
        // dispatch({
        //   type: "SET_DROPDOWN",
        //   payload: { keyName: "Language", data: data.body },
        // });
        dispatch(
          setDependentDropdown({
            keyName: "Sortkey",
            data: data.body || [],
            keyName2: glID,
          })
        )
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getSortKey`,
        "get",
        hSuccess,
        hError
      );
    };



  const getAccountType = (glID) => {
    // alert("comapny Code Call")

    const hSuccess = (data) => {
      //setDropdownDataAccountType(data.body)
      dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Accounttype",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (compCode, glId) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "AccountCurrency",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (compCode, glId) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = (compCode, glId) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "HouseBank",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccontId = (compCode, glId) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getCostElementCategory = (accType, glId) => {

    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "CostEleCategory",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getreconAccountType = (compCode, glID) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "ReconAcc",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getPlanningLevel = (compCode, glID) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Planninglevel",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };

    const getFiledStatusGroup = (compCode, glID) => {
      const hSuccess = (data) => {
        // setDropdownDataAccountType(data.body)
        // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
        dispatch(
          setDependentDropdown({
            keyName: "FieldStsGrp",
            data: data.body || [],
            keyName2: glID,
          })
        )
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };

  const getAccountGroup = (coa, glID) => {

    // "Description": "",
    //         "AccountGroup": "ASST",
    //         "FromAcct": "ASSETS",
    //         "ToAcct": "",
    //         "ChartOfAccount": "ETCN"

    // alert("comapny Code Call")
    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })
      //setDropdownDataAccountType(accGrparr)
      dispatch(
        setDependentDropdown({
          keyName: "AccountGroup",
          data: accGrparr || [],
          keyName2: glID,
        })
      )

      dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getLanguage = (glID) => {
    const hSuccess = (data) => {
      //setDropdownDataLanguage(data.body);
      // dispatch({
      //   type: "SET_DROPDOWN",
      //   payload: { keyName: "Language", data: data.body },
      // });
      dispatch(
        setDependentDropdown({
          keyName: "Language",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getDisplayDataGL = async (requestId) => {
  const isChildPresent = rowData?.childRequestIds !== "Not Available";

  if (reqBench === "true") {
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? requestId : "",
        massCreationId:
          isChildPresent &&
          (RequestType === "Create" ||
            RequestType === "Mass Create" ||
            RequestType === "Create with Upload")
            ? requestId
            : "",
        massChangeId:
          isChildPresent &&
          (RequestType === "Change" ||
            RequestType === "Mass Change" ||
            RequestType === "Change with Upload")
            ? requestId
            : "",
        page: 0,
        size: 10,
      };

    return new Promise((resolve, reject) => {
      const hSuccess = async (response) => {
        const apiResponse = response?.body || [];
        const requestHeaderData = response?.body[0]?.Torequestheaderdata;
        const TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

        dispatch(
          setRequestHeaderPayloadData({
            RequestId: requestHeaderData.RequestId,
            RequestPrefix: requestHeaderData.RequestPrefix,
            ReqCreatedBy: requestHeaderData.ReqCreatedBy,
            ReqCreatedOn: requestHeaderData.ReqCreatedOn,
            ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
            RequestType: requestHeaderData.RequestType,
            RequestDesc: requestHeaderData.RequestDesc,
            RequestStatus: requestHeaderData.RequestStatus,
            RequestPriority: requestHeaderData.RequestPriority,
            FieldName: requestHeaderData.FieldName,
            TemplateName: requestHeaderData.TemplateName,
            Division: requestHeaderData.Division,
            region: requestHeaderData.region,
            leadingCat: requestHeaderData.leadingCat,
            firstProd: requestHeaderData.firstProd,
            launchDate: requestHeaderData.launchDate,
            isBifurcated: requestHeaderData.isBifurcated,
            screenName: requestHeaderData.screenName,
            TotalIntermediateTasks: TotalIntermediateTasks,
          })
        );

        const transformedPayload = transformApiResponseToReduxPayloadGl(apiResponse);

        // Ensure all async field loaders complete
        const allPromises = apiResponse.map(async (item) => {
          const glID = item?.GeneralLedgerID;

          const calls = [];

          if (item?.COA) {
            calls.push(getCompanyCode(item?.COA, glID, item?.CompanyCode, item?.CoCodeToExtend));
            calls.push(getAccountType(glID));
            calls.push(getAccountGroup(item?.COA, glID));
            calls.push(getLanguage(glID));
            calls.push(getSortKey(glID));
          }

          if (item?.CompanyCode) {
            calls.push(getAccountCurrency(item?.CompanyCode, glID));
            calls.push(getTaxCategory(item?.CompanyCode, glID));
            calls.push(getHouseBank(item?.CompanyCode, glID));
            calls.push(getAccontId(item?.CompanyCode, glID));
            calls.push(getreconAccountType(item?.CompanyCode, glID));
            calls.push(getPlanningLevel(item?.CompanyCode, glID));
            calls.push(getFiledStatusGroup(item?.CompanyCode, glID));
          }

          if (item?.Accounttype) {
            calls.push(getCostElementCategory(item?.Accounttype, glID));
          }

          return Promise.all(calls);
        });

        await Promise.all(allPromises); // wait for all data loading to complete

        dispatch(setGLPayload(transformedPayload?.payload));
        dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));
        resolve(); // signal that all is done
      };

      const hError = (error) => {
        console.error("Error:", error);
        reject(error);
      };

      doAjax(
        `/${destination_GeneralLedger_Mass}/data/displayMassGeneralLedger`,
        "post",
        hSuccess,
        hError,
        payload
      );
    });
  }
};

  // const getDisplayDataGL = async (requestId) => {
  //   const isChildPresent = rowData?.childRequestIds !== "Not Available";
  //   if (reqBench === "true") {
  //     const payload = {
  //       sort: "id,asc",
  //       parentId: !isChildPresent ? requestId : "",
  //       massCreationId:
  //         isChildPresent &&
  //         (RequestType === "Create" ||
  //           RequestType === "Mass Create" ||
  //           RequestType === "Create with Upload")
  //           ? requestId
  //           : "",
  //       massChangeId:
  //         isChildPresent &&
  //         (RequestType === "Change" ||
  //           RequestType === "Mass Change" ||
  //           RequestType === "Change with Upload")
  //           ? requestId
  //           : "",
  //       page: 0,
  //       size: 10,
  //     };

  //     const hSuccess = (response) => {
  //       const apiResponse = response?.body || [];
  //       //new added For header Data Constant
  //       let requestHeaderData = response?.body[0]?.Torequestheaderdata;
  //       let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

  //       dispatch(
  //         setRequestHeaderPayloadData({
  //           RequestId: requestHeaderData.RequestId,
  //           RequestPrefix: requestHeaderData.RequestPrefix,
  //           ReqCreatedBy: requestHeaderData.ReqCreatedBy,
  //           ReqCreatedOn: requestHeaderData.ReqCreatedOn,
  //           ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
  //           RequestType: requestHeaderData.RequestType,
  //           RequestDesc: requestHeaderData.RequestDesc,
  //           RequestStatus: requestHeaderData.RequestStatus,
  //           RequestPriority: requestHeaderData.RequestPriority,
  //           FieldName: requestHeaderData.FieldName,
  //           TemplateName: requestHeaderData.TemplateName,
  //           Division: requestHeaderData.Division,
  //           region: requestHeaderData.region,
  //           leadingCat: requestHeaderData.leadingCat,
  //           firstProd: requestHeaderData.firstProd,
  //           launchDate: requestHeaderData.launchDate,
  //           isBifurcated: requestHeaderData.isBifurcated,
  //           screenName: requestHeaderData.screenName,
  //           TotalIntermediateTasks: TotalIntermediateTasks,
  //         })
  //       );
  //       setApiResponses(apiResponse);
  //       const transformedPayload =
  //         transformApiResponseToReduxPayloadGl(apiResponse);
  //       apiResponse.forEach(item => {
  //         let glID = item?.GeneralLedgerID
  //         if (item?.COA) {
  //           getCompanyCode(item?.COA, glID,item?.CompanyCode,item?.CoCodeToExtend)
  //           getAccountType(glID)
  //           getAccountGroup(item?.COA, glID)
  //           getLanguage(glID)
  //           getSortKey(glID)


  //         }
  //         if (item?.CompanyCode) {

  //           getAccountCurrency(item?.CompanyCode, glID)
  //           getTaxCategory(item?.CompanyCode, glID)
  //           getHouseBank(item?.CompanyCode, glID)
  //           getAccontId(item?.CompanyCode, glID)
  //           getreconAccountType(item?.CompanyCode, glID)
  //           getPlanningLevel(item?.CompanyCode, glID)
  //           getFiledStatusGroup(item?.CompanyCode, glID)

            


            
  //           // dispatch(
  //           //   updateModuleFieldDataGL({
  //           //     uniqueId: glID,
  //           //     keyName: "CompanyCode",
  //           //     data: item?.CompanyCode,
  //           //     viewID: "Comp Codes"
  //           //   })
  //           // );
  //         }
  //         if (item?.Accounttype) {
  //           //getAccountGroup(item?.AccountGroup,glID)
  //           //alert(item?.Accounttype)
            
  //           getCostElementCategory(item?.Accounttype, glID)
  //           // dispatch(
  //           //   updateModuleFieldDataGL({
  //           //     uniqueId: glID,
  //           //     keyName: "Accounttype",
  //           //     data: item?.Accounttype,
  //           //     viewID: "Type/Description"
  //           //   })
  //           // );
  //         }

  //       });
  //      // debugger
      
  //     console.log(transformedPayload?.payload,"transformedPayload?.payload")
  //     //alert("come")
 
  //     dispatch(setGLPayload(transformedPayload?.payload))
  //     dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));

  //   }

  //     const hError = (error) => {
  //       console.error("Error fetching GL Create data:", error);
  //     };

  //     doAjax(
  //       `/${destination_GeneralLedger_Mass}/data/displayMassGeneralLedger`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   }
  // };

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if(!isGeneralLedgerOdataApiCalled){
      fetchAllDropdownFMD("generalLedger")
      dispatch(setOdataApiCall(true))
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.GL)

    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
    }
  },[])


  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataGL(RequestId);
        if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }

        setAddHardCodeData(true);
      } else {
        dispatch(setActiveStep(0));
      }
    };

    loadData();
    return () => {
      dispatch(clearChangeLogData());
      dispatch(resetPayloadDataGL())
      dispatch(setRequestHeader({}))
      dispatch(clearCreateTemplateArray());
      dispatch(clearCreateChangeLogDataGL());
      dispatch(setDropDown({ keyName: "FieldName", data: [] }))
    };
  }, [requestId, dispatch]); //reduxPayload

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    }
    else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.PROFIT_CENTER);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false)
  };

  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestId || requestIdHeader ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>
                {requestIdHeader
                  ? requestHeaderSlice?.requestPrefix +
                    "" +
                    requestHeaderSlice?.requestId
                  : `NGLA${requestId}`}
              </span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
          {isChangeLogopen && <ChangeLogGL open={true} closeModal={handleClosemodalData} requestId={requestHeaderData?.RequestId || requestId} requestType={"create"} />}
          {/* {createChangeLogIsOpen && <CreateChangeLog open={true} closeModal={() => setCreateChangeLogIsOpen(false)} requestId={requestIdHeader || requestId.slice(3)} requestType={payloadData?.RequestType} />} */}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => {
                  navigate(
                    `/requestBench/errorHistory?RequestId=${
                      RequestId ? RequestId : ""
                    }`,
                    { state: { display: true } }
                  );
                }}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                // onClick={handleExportTemplateExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>
        <IconButton
          onClick={() => {
            if(reqBench && !ENABLE_STATUSES?.includes(reduxPayload?.requestHeaderData?.RequestStatus)) {
              //alert("come")
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true)
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{left: "-10px",}}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          margin: "25px 14%",
          marginTop: "-35px"
        }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="error" disabled={
                      (index === 1 && !isSecondTabEnabled) ||
                      (index === 2 && !isAttachmentTabEnabled)||
                      (index===3 && !isSecondTabEnabled && !isAttachmentTabEnabled)
                    } onClick={() => handleTabChange(index)} sx={{ fontSize: "50px", fontWeight: "bold" }}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{t(label)}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {tabValue === 0 && (
          <>
            <RequestHeaderGl apiResponse={apiResponses} reqBench={reqBench} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}/>
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={handleDownload}
                setEnableDocumentUpload={setEnableDocumentUpload}
                enableDocumentUpload={enableDocumentUpload}
                handleUploadMaterial={handleUploadGL}
              />
            )}
          </>)}
        {tabValue === 1 && 
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" || requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangeGL
            reqBench={reqBench} 
            requestId={requestId} 
            apiResponses={apiResponses} 
            setIsAttachmentTabEnabled={true} 
            setCompleted={setCompleted} 
            downloadClicked={downloadClicked} 
            setDownloadClicked={setDownloadClicked} />
          ) : 
          (
            <RequestDetailsGL //
                reqBench={reqBench}
                apiResponses={apiResponses}
                setCompleted={setCompleted}
                setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
            )
          )}
      </Box>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: colors?.secondary?.amber, fontSize: "20px" }} />} Title={"Warning"} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              No
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              Yes
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </div>
  );
};

export default GeneralLedgerRequestTab;

              