import useIOFetchDropdownAndDispatch from "./useIOFetchDropdownAndDispatch";
import { destination_InternalOrder } from "../../../destinationVariables";

const useIOdropdownData = () => {
  const { fetchDataAndDispatch } = useIOFetchDropdownAndDispatch();
  const fetchAllDropdownIOData = () => {
    const apiCalls = [
      { url: `/${destination_InternalOrder}/api/v1/lookup/controlling-areas`, keyName: "controllingArea" },
      { url: `/${destination_InternalOrder}/data/getOrderType`, keyName: "orderType" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/company-codes`, keyName: "compCode" },
    ];
    apiCalls.forEach(({ url, keyName }) => {
      fetchDataAndDispatch(url, keyName);
    });
  }
  return { fetchAllDropdownIOData };
};

export default useIOdropdownData;

