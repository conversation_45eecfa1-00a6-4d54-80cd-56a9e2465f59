import { useSnackbar } from './useSnackbar';
import { ERROR_MESSAGES, LOADING_MESSAGE, MODULE, REQUEST_TYPE, SUCCESS_MESSAGES } from '../constant/enum';
import { END_POINTS } from '../constant/apiEndPoints';
import { APP_END_POINTS } from '../constant/appEndPoints';
import { doAjax } from '../components/Common/fetchService';
import { useNavigate } from 'react-router-dom';

const useDownloadExcel = (module) => {
    const { showSnackbar } = useSnackbar();
    const navigate = useNavigate()
    const destination = END_POINTS?.MODULE_DESTINATION_MAP?.[module]

    const handleDownload = (setLoaderMessage, setBlurLoading, payloadFields, module) => {
        setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
        setBlurLoading(true);

        const hSuccess = (response) => {
            if (response?.size == 0) {
                setBlurLoading(false);
                setLoaderMessage("");
                showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
                return;
            }
            const href = URL.createObjectURL(response);
            const link = document.createElement("a");

            link.href = href;
            link.setAttribute(
                "download",
                payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
                    ? `${module}_Mass Change.xlsx`
                    : `${module}_Mass Create.xlsx`
            );
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(href);
            setBlurLoading(false);
            setLoaderMessage("");
            showSnackbar(
                `${payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
                    ? `${module}_Mass Change`
                    : `${module}_Mass Create`
                }.xlsx has been downloaded successfully.`,
                "success"
            );
            setTimeout(() => {
                navigate(`${APP_END_POINTS?.REQUEST_BENCH}`);
            }, 2600);
        };
        const hError = () => {
            setBlurLoading(false);
        };

        const downloadUrl = `/${destination}${payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
            ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD
            : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD
            }`;
        doAjax(downloadUrl, "getblobfile", hSuccess, hError);
    };

    const handleEmailDownload = () => {
        setBlurLoading(true);
        const hSuccess = () => {
            setBlurLoading(false);
            setLoaderMessage("");
            showSnackbar(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED, "success");
            setTimeout(() => {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
            }, 2600);
        };
        const hError = () => {
            setBlurLoading(false);
            showSnackbar(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error");
            setTimeout(() => {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
            }, 2600);
        };

        const downloadUrl = `/${destination}${payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
                ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL
                : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL
            }`;

        doAjax(downloadUrl, "get", hSuccess, hError);
    };

    return {
        handleDownload,
        handleEmailDownload
    };
};

export default useDownloadExcel;
