import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import useLang from "@hooks/useLang";
import { useDispatch, useSelector } from "react-redux";
import {
  DECISION_TABLE_NAME,
  ENABLE_STATUSES,
  ERROR_MESSAGES,
  MODULE,
  REGION_CODE,
  REQUEST_TYPE,
  SUCCESS_MESSAGES,
} from "@constant/enum";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import { DataGrid } from "@mui/x-data-grid";
import { useLocation } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import {
  CancelOutlined as CancelOutlinedIcon,
  DeleteOutlineOutlined as DeleteOutlineOutlinedIcon,
  TaskAlt as TaskAltIcon,
} from "@mui/icons-material";
import {
  setBomRows,
  setSelectedRowID,
  clearTabData,
} from "@BillOfMaterial/bomSlice";
import { updatePage } from "@app/paginationSlice";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import useGenericDtCall from "@hooks/useGenericDtCall";
import BOMViews from "./components/BOMViews";
import { useSnackbar } from "@hooks/useSnackbar";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import AddBOMDialog from "./components/AddBOMDialog";
import dayjs from "dayjs";
import { createBOMPayload } from "../../functions";
import { destination_BOM } from "../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";

const BOMListDetails = (props) => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getButtonDT, dtData: dtButtonData } = useGenericDtCall();
  const { showSnackbar } = useSnackbar();

  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const storedRows = useSelector((state) => state.bom.bomRows);
  const selectedRowID = useSelector((state) => state.bom.selectedRowID);
  const paginationData = useSelector((state) => state.paginationData);
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const bomRows = useSelector((state) => state.bom.bomRows);
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);

  const [rows, setRows] = useState(storedRows || []);
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [viewNames, setViewNames] = useState([]);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [page, setPage] = useState(0);
  const [validatedRows, setValidatedRows] = useState({});

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const disableCheck =
    !ENABLE_STATUSES.includes(props?.requestStatus) ||
    (isrequestId && !isreqBench);

  useEffect(() => {
    if (
      storedRows?.length === 0 &&
      (payloadFields?.RequestType?.code === REQUEST_TYPE.CREATE ||
        payloadFields?.RequestType?.code === REQUEST_TYPE.CREATE_WITH_UPLOAD)
    ) {
      setOpenAddMatPopup(true);
    }
    const payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_BOM_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": REGION_CODE.US,
          "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": "Z_MAT_REQ_INITIATE",
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": REQUEST_TYPE.CREATE,
        },
      ],
    };
    getDtCall(payload);
    fetchButtonsFromDt();
  }, []);

  useEffect(() => {
    if (dtData?.result?.length) {
      const viewNameSequenceMap = new Map();

      dtData.result.forEach((item) => {
        item.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE?.forEach((field) => {
          const viewName = field.MDG_MAT_VIEW_NAME;
          const sequence = field.MDG_MAT_VIEW_SEQUENCE;

          if (viewName && viewName !== "Header" && sequence !== undefined) {
            if (
              !viewNameSequenceMap.has(viewName) ||
              viewNameSequenceMap.get(viewName) > sequence
            ) {
              viewNameSequenceMap.set(viewName, sequence);
            }
          }
        });
      });

      const sortedViewNames = Array.from(viewNameSequenceMap.entries())
        .sort(([, sequenceA], [, sequenceB]) => sequenceA - sequenceB)
        .map(([viewName]) => viewName);

      setViewNames(sortedViewNames);
    }
  }, [dtData]);

  const fetchButtonsFromDt = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_BOM_BUTTONS,
      version: "v3",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME": MODULE.BOM,
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":
            payloadFields?.RequestType?.code,
        },
      ],
    };
    getButtonDT(payload);
  };

  useEffect(() => {
    if (rows.length > 0) {
      const initialValidationState = {};
      rows.forEach((row) => {
        if (!(row.id in validatedRows)) {
          initialValidationState[row.id] = undefined;
        }
      });
      setValidatedRows((prev) => ({
        ...prev,
        ...initialValidationState,
      }));
    }
  }, [rows]);

  const handleCellEdit = (params) => {
    const { id, field, value } = params;
    const updatedRows = rows.map((row) =>
      row.id === id
        ? {
            ...row,
            [field]:
              (field === "validFrom" || field === "validTo") && value
                ? dayjs(value)
                : value,
          }
        : row
    );
    setRows(updatedRows);
    dispatch(setBomRows(updatedRows));
  };
  const handlePageChange = (newPage) => {
    dispatch(updatePage(newPage));
    setPage(newPage);
  };
  const handleRowSelection = (params) => {
    dispatch(setSelectedRowID(params.row.id));
  };

  const AddBOM = () => {
    setOpenAddMatPopup(false);
    if (withReference === "yes") {
      return;
    } else {
      handleAddRow();
    }
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      material: "",
      plant: "",
      bomUsage: "",
      altBom: "",
      validFrom: dayjs(),
      validTo: dayjs(),
    };
    dispatch(setBomRows([...rows, newRow]));
    setRows([...rows, newRow]);
    setValidatedRows((prev) => ({
      ...prev,
      [id]: undefined,
    }));
  };

  const checkDuplicateCombination = (currentRow) => {
    const { plant, bomUsage, altBom } = currentRow;

    if (!plant || !bomUsage || !altBom) {
      return {
        isValid: false,
        message: t(ERROR_MESSAGES.MANDATORY_FILTER_BOM),
      };
    }

    const combination = `${plant}-${bomUsage}-${altBom}`;
    const duplicateRows = rows.filter(
      (row) =>
        row.id !== currentRow.id &&
        row.plant === plant &&
        row.bomUsage === bomUsage &&
        row.altBom === altBom
    );

    if (duplicateRows.length > 0) {
      return {
        isValid: false,
        message: `${t(
          ERROR_MESSAGES.DUPLICATE_COMBINATION
        )}: Plant ${plant}, BOM Usage ${bomUsage}, Alternative BOM ${altBom}`,
      };
    }

    return {
      isValid: true,
      message: t(SUCCESS_MESSAGES.DUPLICATE_COMBINATION),
    };
  };

  const handleValidateRow = (row) => {
    const validation = checkDuplicateCombination(row);

    showSnackbar(validation.message, validation.isValid ? "success" : "error");
    setValidatedRows((prev) => ({
      ...prev,
      [row.id]: validation.isValid,
    }));
  };

  const handleSaveAsDraft = () => {
    const payload = createBOMPayload(
      bomRows,
      tabFieldValues,
      payloadFields,
      createdRequestId
    );

    doAjax(
      `/${destination_BOM}/massAction/createBOMSaveAsDraft`,
      "post",
      (data) => {
        showSnackbar("BOM saved as draft successfully", "success");
      },
      (err) => {
        showSnackbar("Error occurred while saving BOM as draft", "error");
      },
      payload
    );
  };

  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={disableCheck}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "material",
      headerName: t("Material"),
      flex: 0.5,
      editable: true,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["Material"] || []}
            value={params.row.material || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "material",
                value: newValue,
              })
            }
            placeholder={t("Select Material")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "plant",
      headerName: t("Plant"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["Plant"] || []}
            value={params.row.plant || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "plant",
                value: newValue,
              })
            }
            placeholder={t("Select Plant")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "bomUsage",
      headerName: t("BOM Usage"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["BOMUsage"] || []}
            value={params.row.bomUsage || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "bomUsage",
                value: newValue,
              })
            }
            placeholder={t("Select BOM Usage")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "altBom",
      headerName: t("Alternative BOM"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["BOM"] || []}
            value={params.row.altBom || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "altBom",
                value: newValue,
              })
            }
            placeholder={t("Select Alternative BOM")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "validFrom",
      headerName: t("Valid From"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={params.row.validFrom}
              onChange={(newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "validFrom",
                  value: newValue,
                })
              }
              disabled={disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  sx: {
                    width: "90%",
                    "& .MuiInputBase-root": {
                      height: 36,
                    },
                    "& .MuiInputBase-input": {
                      padding: "8.5px 14px",
                    },
                  },
                  size: "small",
                },
              }}
            />
          </LocalizationProvider>
        );
      },
    },
    {
      field: "validTo",
      headerName: t("Valid To"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              value={params.row.validTo}
              onChange={(newValue) =>
                handleCellEdit({
                  id: params.row.id,
                  field: "validTo",
                  value: newValue,
                })
              }
              disabled={disableCheck}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  sx: {
                    width: "90%",
                    "& .MuiInputBase-root": {
                      height: 36,
                    },
                    "& .MuiInputBase-input": {
                      padding: "8.5px 14px",
                    },
                  },
                  size: "small",
                },
              }}
            />
          </LocalizationProvider>
        );
      },
    },
    {
      field: "Actions",
      headerName: t("Actions"),
      flex: 0.7,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const validateStatus = validatedRows[params.row.id] || undefined;
        return (
          <Stack
            direction="row"
            alignItems="center"
            sx={{ marginLeft: "0.5rem", magrinRight: "0.5rem" }}
            spacing={0.5}
          >
            {!disableCheck && (
              <Tooltip title="Delete Row">
                <IconButton
                  onClick={() => {
                    const updatedRows = rows.filter(
                      (row) => row.id !== params.row.id
                    );
                    setRows(updatedRows);
                    dispatch(setBomRows(updatedRows));
                    dispatch(clearTabData(params.row.id));
                    setValidatedRows((prev) => {
                      const newValidatedRows = { ...prev };
                      delete newValidatedRows[params.row.id];
                      return newValidatedRows;
                    });
                  }}
                  color="error"
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip
              title={
                validateStatus === true
                  ? "Validated Successfully"
                  : validateStatus === false
                  ? t("Validation Failed")
                  : t("Click to Validate")
              }
            >
              <IconButton
                onClick={() => handleValidateRow(params.row)}
                color={
                  validateStatus === true
                    ? "success"
                    : validateStatus === false
                    ? "error"
                    : "default"
                }
              >
                {validateStatus === false ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
          </Stack>
        );
      },
    },
  ];

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };
  return (
    <div>
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of BOM")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={() => {
                  if (
                    payloadFields?.RequestType?.code === REQUEST_TYPE.CREATE
                  ) {
                    setOpenAddMatPopup(true);
                  }
                }}
              >
                + {t("Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowCount={paginationData?.totalElements || 0}
                rowsPerPageOptions={[50]}
                onRowClick={handleRowSelection}
                onCellEditCommit={handleCellEdit}
                onPageChange={(newPage) => handlePageChange(newPage)}
                pagination
                disableSelectionOnClick
                getRowClassName={(params) =>
                  params.id === selectedRowID ? "selected-row" : ""
                }
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed
                    ? "calc(100vh - 150px)"
                    : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>

      {selectedRowID && rows.find((row) => row.id === selectedRowID) && (
        <BOMViews
          isTabsZoomed={isTabsZoomed}
          toggleTabsZoom={toggleTabsZoom}
          viewNames={viewNames}
          selectedRowID={selectedRowID}
          viewsDt={dtData?.result}
          t={t}
        />
      )}
      {openAddMatPopup && (
        <AddBOMDialog
          open={openAddMatPopup}
          onClose={() => setOpenAddMatPopup(false)}
          withReference={withReference}
          setWithReference={setWithReference}
          onProceed={AddBOM}
          t={t}
        />
      )}
      <BottomNavGlobal
        filteredButtons={
          dtButtonData?.result?.[0]?.MDG_MAT_DYN_BUTTON_CONFIG || []
        }
        moduleName={MODULE.BOM}
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={() => {}}
        handleSubmitForApprove={() => {}}
        handleSendBack={() => {}}
        handleCorrection={() => {}}
        handleRejectAndCancel={() => {}}
        handleValidateAndSyndicate={() => {}}
        validateAllRows={() => {}}
      />
    </div>
  );
};

export default BOMListDetails;