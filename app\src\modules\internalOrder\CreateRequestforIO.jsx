import React, { useEffect, useState } from "react";
import { <PERSON>, Button, DialogActions, Dialog<PERSON>ontent, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Stepper, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { ArrowCircleLeftOutlined, WarningOutlined } from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { button_Outlined, button_Primary } from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import RequestHeaderIO from "./RequestHeaderIO";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { useDispatch, useSelector } from "react-redux";
import { setRequestHeaderDTIO, setTabValue } from "./slice/internalOrderSlice";
import { setLocalStorage } from "@helper/glhelper";
import { LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import RequestDetailsIO from "./RequestDetailsIO";
import useIOdropdownData from "./hooks/useIOdropdownData";
import ReusableAttachmentAndComments from "@components/Common/ReusableAttachmentAndComments";
import ReusablePreview from "@components/Common/ReusablePreview";

const CreateRequestforIO = () => {
  const { t } = useLang();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const { getDtCall, dtData } = useGenericDtCall();
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const { fetchAllDropdownIOData } = useIOdropdownData();
  const savedRequestData = useSelector((state) => state.internalOrder.savedReqData);
  const tabValue = useSelector((state) => state.internalOrder.tabValue);

  const steps = [t("Request Header"), t("Internal Order List"), t("Attachments & Remarks"), t("Preview")];

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.INTERNAL_ORDER);
    }
  };
  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_FMD_REQUEST_HEADER_CONFIG",
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": "Internal Order",
        },
      ],
    };
    getDtCall(payload);
  };
  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setRequestHeaderDTIO(requestHeaderObj));
    }
  }, [dtData]);
  useEffect(() => {
    fetchHeaderFieldsFromDt();
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.IO);
  }, []);

  useEffect(() => {
    fetchAllDropdownIOData();
  }, []);

  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Request Header ID")}: <span>{savedRequestData?.RequestId}</span>
          </Typography>
        </Grid>

        <IconButton onClick={() => setisDialogVisible(true)} color="primary" title={t("Back")} sx={{ left: "-10px" }}>
          <ArrowCircleLeftOutlined sx={{ fontSize: "25px", color: "#000000" }} />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{ justifyContent: "center", margin: "25px 14%", marginTop: "-35px" }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton onClick={() => handleTabChange(index)}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        <Box sx={{ padding: "20px", borderRadius: "8px" }}>
          {tabValue === 0 && <RequestHeaderIO setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} />}
          {tabValue === 1 && <RequestDetailsIO />}
          {tabValue === 2 && (
            <Box>
              <ReusableAttachmentAndComments
                module={MODULE_MAP.IO}
                requestId={savedRequestData?.RequestId}
              />
            </Box>
          )}
          {tabValue === 3 && (
            <Box>
              <Typography variant="h6">{t("Preview")}</Typography>
            </Box>
          )}
        </Box>
      </Box>

      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined sx={{ color: colors.secondary.amber, fontSize: "20px" }} />} Title={t("Warning")} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{t("Are you sure you want to leave this page?")}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={button_Outlined} onClick={handleCancel}>
              {t("No")}
            </Button>
            <Button variant="contained" size="small" sx={button_Primary} onClick={handleYes}>
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CreateRequestforIO;
