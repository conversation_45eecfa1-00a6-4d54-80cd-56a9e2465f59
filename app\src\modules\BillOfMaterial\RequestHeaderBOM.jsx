import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { container_Padding } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { ToastContainer } from "react-toastify";
import { useSnackbar } from "@hooks/useSnackbar";
import {
  REQUEST_HEADER_FILED_NAMES,
  REQUEST_PRIORITY,
  REQUEST_TYPE_OPTIONS,
  SUCCESS_MESSAGES,
  VISIBILITY_TYPE,
} from "@constant/enum";
import { setDropDown } from "@app/dropDownDataSlice";
import { setMultipleMaterialPayloadKey, setPayload } from "@app/payloadSlice";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { DECISION_TABLE_NAME } from "@constant/enum";
import {
  setBOMpayloadData,
  setDropDownDataBOM,
  setHeaderFieldsBOM,
  setRequestHeaderID,
  setTabValue,
} from "./bomSlice";
import { TEMPLATE_NAMES_BOM } from "@constant/changeTemplates";
import { destination_BOM } from "../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import FilterFieldGlobal from "@components/MasterDataCockpit/FilterFieldGlobal";

const RequestHeaderBOM = ({
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
}) => {
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getChangeTemplate, dtData: dtChangeTemplateFields } =
    useGenericDtCall();
  const { t } = useLang();
  const { showSnackbar } = useSnackbar();
  const requestHeaderFields = useSelector((state) => state.bom.headerFieldsBOM);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");
  const requestHeaderCreatedId = useSelector(
    (state) => state.bom.requestHeaderID
  );
  const userData = useSelector((state) => state.userManagement.userData);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);

  const dispatch = useDispatch();
  dispatch(setDropDownDataBOM({ keyName: "RequestPriority", data: REQUEST_PRIORITY }));
  dispatch(setDropDownDataBOM({ keyName: "TemplateName", data: TEMPLATE_NAMES_BOM }));
  dispatch(
    setDropDownDataBOM({
      keyName: REQUEST_HEADER_FILED_NAMES?.REQUEST_TYPE, data: REQUEST_TYPE_OPTIONS
    })
  );
  if (!isWorkspace && !isreqBench) {
    dispatch(
      setBOMpayloadData({ keyName:"ReqCreatedBy", data: userData?.user_id })
    );
    dispatch(
      setBOMpayloadData({ keyName:"RequestStatus", data: "DRAFT" })
    );
  }
  useEffect(() => {
    fetchHeaderFieldsFromDt();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    fetchChangeTemplateFields();
  }, [payloadFields?.TemplateName]);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBOM(requestHeaderObj));
    }
  }, [dtData]);
  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (
      payloadFields &&
      requestHeaderFields[Object.keys(requestHeaderFields)]?.length
    ) {
      requestHeaderFields[Object.keys(requestHeaderFields)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.visibility === VISIBILITY_TYPE?.MANDATORY
          ) {
            allFilled = false;
          }
        }
      );
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO":
            payloadFields?.RequestType?.code || "Create",
          "MDG_CONDITIONS.MDG_MODULE": "BOM",
        },
      ],
    };
    getDtCall(payload);
  };

  const fetchChangeTemplateFields = () => {
    // let payload = {
    //   decisionTableId: null,
    //   decisionTableName: DECISION_TABLE_NAME.MDG_MAT_BOM_CHANGE_TEMPLATE,
    //   version: "v2",
    //   conditions: [
    //     {
    //       "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Create",
    //       "MDG_CONDITIONS.MDG_MODULE": "BOM",
    //     },
    //   ],
    // };
    // getChangeTemplate(payload);
  };

  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const currentDate = `/Date(${Date.now()})/`;
    const payload = {
      RequestId: requestHeaderCreatedId ? requestHeaderCreatedId : "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType.code || "",
      RequestPrefix: "",
      RequestPriority: payloadFields?.RequestPriority?.code || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: "DRAFT",
      TemplateName: payloadFields?.TemplateName || "",
      FieldName: payloadFields?.FieldName?.join("$^$") || "",
      Region: payloadFields?.Region?.code || "",
      filterDetails: "",
      SapSystem: "",
      IsBifurcated: true,
      IncompleteChildTasks: "",
    };
    const hSuccess = (data) => {
      showSnackbar(
        `${t(SUCCESS_MESSAGES.REQUEST_HEADER_CREATED)} ${data?.body?.requestId}`,
        "success"
      );
      dispatch(setRequestHeaderID(data?.body?.requestId));
      setIsAttachmentTabEnabled(true);
      //   dispatch(updateAllTabsData({}))
      //   dispatch(setTaskData({}))
      //   if (initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
      //     setOpenDownloadDialog(true);
      //     return
      //   }
      //   if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
      //     setDialogOpen(true);
      //     return;
      //   }
      //   if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
      //     const filteredConfig = filterConfigData(changeData?.["Config Data"], initialPayload?.FieldName, ["Material", "Plant", "Sales Org", "Distribution Channel", "Warehouse", "MRP Controller"]);
      //     dispatch(changeTemplateDT({ ...changeData, "Config Data": filteredConfig }));
      //     const filteredTableNames = filterFieldNameData(changeData?.[initialPayload?.TemplateName], initialPayload?.FieldName)
      //     dispatch(setChangeTableData([...filteredTableNames]));
      //   }
      dispatch(setTabValue(1));
      setIsSecondTabEnabled(true);
    };
    const hError = () => {
      showSnackbar("Error occured while saving Request Header", "error");
    };

    doAjax(
      `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_REQUEST}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderFields).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {t(key)}
            </Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterFieldGlobal
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={{}}
                      module={"BOM"}
                      disabled={isWorkspace || requestHeaderCreatedId}
                      requestHeader={true}
                    />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderCreatedId && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "20px",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleButtonClick}
                  disabled={!checkAllFieldsFilled()}
                >
                  {t("Save Request Header")}
                </Button>
              </Box>
            )}
            <ToastContainer />
          </Grid>
        ))}
      </Stack>
    </div>
  );
};

export default RequestHeaderBOM;
