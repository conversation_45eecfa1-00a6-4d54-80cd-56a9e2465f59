
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  requestHeaderDTIO: {},
  tabValue:0,
  dropDownDataIO: {},
  IOpayloadData: {
    requestHeaderData: {},
    rowsHeaderData: [],
    rowsBodyData: {},
  },
  requestHeaderID: "",
  savedReqData: {},
  fieldConfigByOrderType: {}, // Store field config by order type
};

const internalOrderSlice = createSlice({
  name: "internalOrder",
  initialState,
  reducers: {
    setRequestHeaderDTIO: (state, action) => {
      state.requestHeaderDTIO = action.payload;
    },
    clearRequestHeaderIO: (state) => {
      state.requestHeaderDTIO = {};
    },
    setDropDownDataIO: (state, action) => {
      const { keyName, data } = action.payload;
      state.dropDownDataIO[keyName] = data;
    },
    setIOpayloadData: (state, action) => {
      const { keyName, data } = action.payload;
      state.IOpayloadData[keyName] = data;
    },
    updateModuleFieldDataIO: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;
      const value = data?.code ?? data ?? "";
      if (uniqueId) {
        if (!state.IOpayloadData.rowsBodyData[uniqueId]) {
          state.IOpayloadData.rowsBodyData[uniqueId] = {};
        }
        state.IOpayloadData.rowsBodyData[uniqueId][keyName] = value;
      } else {
        state.IOpayloadData.requestHeaderData[keyName] = value;
      }
    },
    removeRowDataIO: (state, action) => {
      const { uniqueId } = action.payload;
      if (uniqueId && state.IOpayloadData.rowsBodyData[uniqueId]) {
        delete state.IOpayloadData.rowsBodyData[uniqueId];
      }
    },
    setInternalOrderFieldConfig: (state, action) => {
      console.log("setInternalOrderFieldConfig reducer called with:", action.payload);
      const { orderType, fieldData } = action.payload;
      if (!state.fieldConfigByOrderType) {
        state.fieldConfigByOrderType = {};
      }
      state.fieldConfigByOrderType[orderType] = fieldData;
      console.log("Updated fieldConfigByOrderType:", state.fieldConfigByOrderType);
    },
    setRequestHeaderIDIO: (state, action) => {
      state.requestHeaderID = action.payload;
    },
    setSavedReqData: (state, action) => {
      state.savedReqData = action.payload;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
  },
});

export const {
  setRequestHeaderDTIO,
  clearRequestHeaderIO,
  setDropDownDataIO,
  setIOpayloadData,
  setRequestHeaderIDIO,
  updateModuleFieldDataIO,
  removeRowDataIO,
  setInternalOrderFieldConfig,
  setSavedReqData,
  setTabValue
} = internalOrderSlice.actions;

export default internalOrderSlice.reducer;
